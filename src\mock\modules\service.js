/**
 * 服务项目模块Mock数据

 */

import Mock from 'mockjs'
import { successResponse, pageResponse, createCrudMock } from '../utils.js'

console.log('🔧 正在加载服务项目模块Mock数据...')


const serviceList = Mock.mock({
  'list|30-50': [{
    'id|+1': 1,
    'title': '@ctitle(5, 15)', // 项目名称
    'name': '@ctitle(5, 15)', // 兼容字段
    'cover': '@image("200x200", "#409eff", "#fff", "服务")', // 封面图
    'images|1-3': ['@image("200x200", "#409eff", "#fff", "服务")'], // 图片数组
    'service_cate_id': {
      'id|1-10': 1,
      'name': '@pick(["家政服务", "维修服务", "清洁服务", "搬家服务", "装修服务", "保洁服务", "月嫂服务", "育儿嫂", "护工服务", "钟点工"])'
    },
    'category': '@pick(["家政服务", "维修服务", "清洁服务", "搬家服务", "装修服务"])', // 兼容字段
    'price|100-2000.2': 1, // 价格
    'unit': '@pick(["次", "小时", "天", "项", "套餐"])', // 单位
    'description': '@cparagraph(2, 4)', // 描述
    'content': '@cparagraph(3, 6)', // 详细内容
    'top|1-100': 1, // 排序值（使用top字段）
    'sort|1-100': 1, // 兼容字段
    'status|1': [0, 1], // 0-禁用 1-启用
    'is_recommend|1': [0, 1], // 是否推荐
    'view_count|100-9999': 1, // 浏览量
    'order_count|10-500': 1, // 订单量
    'create_time': '@datetime', // 使用下划线格式
    'update_time': '@datetime',
    'createTime': '@datetime', // 兼容字段
    'updateTime': '@datetime'
  }]
}).list

const categoryList = Mock.mock({
  'list|15-25': [{
    'id|+1': 1,
    'name': '@ctitle(3, 8)', // 分类名称
    'parent_id|0-5': 0, // 父级ID，0为顶级分类
    'parent_name': function() {
      return this.parent_id === 0 ? null : '@ctitle(3, 8)'
    },
    'icon': '@pick(["Service", "Brush", "Tools", "Van", "House", "Setting", "User", "Document"])', // Element Plus图标
    'image': '@image("100x100", "#409eff", "#fff", "分类")', // 分类图片
    'sort|1-100': 1, // 排序
    'is_recommend|1': [0, 1], // 是否推荐
    'status|1': [0, 1], // 状态
    'level|1-3': 1, // 分类层级
    'children': [], // 子分类
    'hasChildren': function() {
      return this.level < 3 && Mock.Random.boolean()
    },
    'create_time': '@datetime',
    'update_time': '@datetime',
    'createTime': '@datetime', // 兼容字段
    'updateTime': '@datetime'
  }]
}).list

// 构建树形结构
const buildCategoryTree = (list) => {
  const tree = []
  const map = {}

  // 创建映射
  list.forEach(item => {
    map[item.id] = { ...item, children: [] }
  })

  // 构建树形结构
  list.forEach(item => {
    if (item.parent_id === 0) {
      tree.push(map[item.id])
    } else if (map[item.parent_id]) {
      map[item.parent_id].children.push(map[item.id])
      map[item.parent_id].hasChildren = true
    }
  })

  return tree
}

// 更新分类列表为树形结构
const categoryTreeList = buildCategoryTree(categoryList)


const bannerList = Mock.mock({
  'list|8-15': [{
    'id|+1': 1,
    'title': '@ctitle(5, 15)', // 轮播图标题
    'image': '@image("750x300", "#409eff", "#fff", "轮播图")', // 轮播图片
    'link': '@pick(["/service/list", "/market/list", "/shop/order", "", "https://www.example.com"])', // 链接地址
    'type|1': [1, 2, 3], // 1-内部链接 2-外部链接 3-无链接
    'position|1': [1, 2, 3], // 1-首页 2-服务页 3-个人中心
    'sort|1-100': 1, // 排序
    'status|1': [0, 1], // 状态 0-禁用 1-启用
    'click_count|100-9999': 1, // 点击量
    'start_time': '@datetime', // 开始时间
    'end_time': '@datetime', // 结束时间
    'create_time': '@datetime',
    'update_time': '@datetime',
    'createTime': '@datetime', // 兼容字段
    'updateTime': '@datetime'
  }]
}).list


const jingangList = Mock.mock({
  'list|8-12': [{
    'id|+1': 1,
    'name': '@ctitle(2, 6)', // 金刚区名称
    'icon': '@pick(["Service", "Document", "User", "Setting", "ShoppingCart", "Star", "Phone", "Location"])', // Element Plus图标
    'image': '@image("60x60", "#409eff", "#fff", "图标")', // 图标图片
    'link': '@pick(["/service/list", "/shop/order", "/custom/list", "/technician/list", "", "pages/service/index"])', // 链接地址
    'type|1': [1, 2], // 1-内部页面 2-外部链接
    'position|1-8': 1, // 位置序号
    'sort|1-100': 1, // 排序
    'status|1': [0, 1], // 状态
    'click_count|50-999': 1, // 点击量
    'create_time': '@datetime',
    'update_time': '@datetime',
    'createTime': '@datetime', // 兼容字段
    'updateTime': '@datetime'
  }]
}).list


const servicePointList = Mock.mock({
  'list|20-35': [{
    'id|+1': 1,
    'name': '@ctitle(5, 15)', // 服务点名称
    'code': '@word(6, 10)', // 服务点编码
    'address': '@county(true)', // 详细地址
    'province': '@province', // 省份
    'city': '@city', // 城市
    'district': '@county', // 区县
    'contact': '@cname', // 联系人
    'phone': '@phone', // 联系电话
    'email': '@email', // 邮箱
    'latitude|30-40.6': 1, // 纬度
    'longitude|110-120.6': 1, // 经度
    'service_radius|5-50': 1, // 服务半径(公里)
    'service_types|1-5': ['@pick(["家政", "维修", "清洁", "搬家", "装修"])'], // 服务类型
    'working_hours': '08:00-22:00', // 工作时间
    'description': '@cparagraph(1, 2)', // 描述
    'status|1': [0, 1], // 状态 0-停用 1-启用
    'is_default|1': [0, 1], // 是否默认服务点
    'order_count|10-500': 1, // 订单数量
    'rating|3-5.1': 1, // 评分
    'create_time': '@datetime',
    'update_time': '@datetime',
    'createTime': '@datetime', // 兼容字段
    'updateTime': '@datetime'
  }]
}).list

const configList = Mock.mock({
  'list|15-25': [{
    'id|+1': 1,
    'key': '@pick(["service_fee_rate", "platform_commission", "min_order_amount", "max_order_amount", "service_time_limit", "cancel_time_limit", "refund_time_limit", "auto_confirm_time", "review_time_limit", "distance_limit"])', // 配置键
    'name': function() {
      const keyNameMap = {
        'service_fee_rate': '服务费率',
        'platform_commission': '平台佣金比例',
        'min_order_amount': '最小订单金额',
        'max_order_amount': '最大订单金额',
        'service_time_limit': '服务时长限制',
        'cancel_time_limit': '取消时间限制',
        'refund_time_limit': '退款时间限制',
        'auto_confirm_time': '自动确认时间',
        'review_time_limit': '评价时间限制',
        'distance_limit': '服务距离限制'
      }
      return keyNameMap[this.key] || '@ctitle(5, 15)'
    },
    'value': function() {
      const valueMap = {
        'service_fee_rate': '@float(0.01, 0.1, 2, 2)',
        'platform_commission': '@float(0.05, 0.2, 2, 2)',
        'min_order_amount': '@integer(50, 100)',
        'max_order_amount': '@integer(5000, 10000)',
        'service_time_limit': '@integer(2, 8)',
        'cancel_time_limit': '@integer(30, 120)',
        'refund_time_limit': '@integer(7, 30)',
        'auto_confirm_time': '@integer(24, 72)',
        'review_time_limit': '@integer(7, 15)',
        'distance_limit': '@integer(10, 50)'
      }
      return valueMap[this.key] || '@word(10, 50)'
    },
    'type': function() {
      const typeMap = {
        'service_fee_rate': 'number',
        'platform_commission': 'number',
        'min_order_amount': 'number',
        'max_order_amount': 'number',
        'service_time_limit': 'number',
        'cancel_time_limit': 'number',
        'refund_time_limit': 'number',
        'auto_confirm_time': 'number',
        'review_time_limit': 'number',
        'distance_limit': 'number'
      }
      return typeMap[this.key] || '@pick(["string", "number", "boolean", "json"])'
    },
    'unit': function() {
      const unitMap = {
        'service_fee_rate': '%',
        'platform_commission': '%',
        'min_order_amount': '元',
        'max_order_amount': '元',
        'service_time_limit': '小时',
        'cancel_time_limit': '分钟',
        'refund_time_limit': '天',
        'auto_confirm_time': '小时',
        'review_time_limit': '天',
        'distance_limit': '公里'
      }
      return unitMap[this.key] || ''
    },
    'description': function() {
      const descMap = {
        'service_fee_rate': '平台收取的服务费率，按订单金额百分比计算',
        'platform_commission': '平台向服务商收取的佣金比例',
        'min_order_amount': '用户下单的最小金额限制',
        'max_order_amount': '用户下单的最大金额限制',
        'service_time_limit': '单次服务的最长时间限制',
        'cancel_time_limit': '订单创建后允许取消的时间限制',
        'refund_time_limit': '订单完成后允许申请退款的时间限制',
        'auto_confirm_time': '订单自动确认完成的时间',
        'review_time_limit': '订单完成后允许评价的时间限制',
        'distance_limit': '服务商接单的最大距离限制'
      }
      return descMap[this.key] || '@cparagraph(1, 2)'
    },
    'group': '@pick(["基础配置", "订单配置", "支付配置", "服务配置", "系统配置"])', // 配置分组
    'is_system|1': [0, 1], // 是否系统配置
    'status|1': [0, 1], // 状态
    'create_time': '@datetime',
    'update_time': '@datetime',
    'createTime': '@datetime', // 兼容字段
    'updateTime': '@datetime'
  }]
}).list

// 设置Mock拦截
Mock.setup({
  timeout: '200-500'
})

// 直接注册API，不使用通用函数
console.log('🔧 开始注册服务项目API...')

// 服务项目列表
Mock.mock(/\/api\/service\/list/, 'get', (options) => {
  console.log('📋 服务项目列表API被调用:', options.url)
  const url = new URL('http://localhost' + options.url)
  const page = parseInt(url.searchParams.get('page') || '1')
  const pageSize = parseInt(url.searchParams.get('pageSize') || '10')
  const keyword = url.searchParams.get('keyword') || ''

  let filteredData = [...serviceList]
  if (keyword) {
    filteredData = serviceList.filter(item =>
      JSON.stringify(item).toLowerCase().includes(keyword.toLowerCase())
    )
  }

  const start = (page - 1) * pageSize
  const end = start + pageSize
  const list = filteredData.slice(start, end)

  return pageResponse(list, filteredData.length, page, pageSize)
})

// 服务项目新增
Mock.mock(/\/api\/service\/add$/, 'post', (options) => {
  console.log('➕ 服务项目新增API被调用:', options.body)
  const newItem = JSON.parse(options.body)
  newItem.id = serviceList.length + 1
  newItem.create_time = Mock.Random.datetime()
  newItem.update_time = Mock.Random.datetime()
  serviceList.push(newItem)
  return successResponse(newItem, '新增成功')
})

// 服务项目更新
Mock.mock(/\/api\/service\/update\/\d+/, 'put', (options) => {
  console.log('✏️ 服务项目更新API被调用:', options.url, options.body)
  const id = parseInt(options.url.match(/\/update\/(\d+)/)[1])
  const updateData = JSON.parse(options.body)
  const index = serviceList.findIndex(item => item.id === id)

  if (index !== -1) {
    serviceList[index] = { ...serviceList[index], ...updateData, update_time: Mock.Random.datetime() }
    return successResponse(serviceList[index], '更新成功')
  } else {
    return errorResponse('数据不存在', 404)
  }
})

// 服务项目删除
Mock.mock(/\/api\/service\/delete\/\d+/, 'delete', (options) => {
  console.log('🗑️ 服务项目删除API被调用:', options.url)
  const id = parseInt(options.url.match(/\/delete\/(\d+)/)[1])
  const index = serviceList.findIndex(item => item.id === id)

  if (index !== -1) {
    serviceList.splice(index, 1)
    return successResponse(null, '删除成功')
  } else {
    return errorResponse('数据不存在', 404)
  }
})

// 轮播图列表
Mock.mock(/\/api\/service\/banner/, 'get', (options) => {
  console.log('🖼️ 轮播图列表API被调用:', options.url)
  const url = new URL('http://localhost' + options.url)
  const page = parseInt(url.searchParams.get('page') || '1')
  const pageSize = parseInt(url.searchParams.get('pageSize') || '10')

  const start = (page - 1) * pageSize
  const end = start + pageSize
  const list = bannerList.slice(start, end)

  return pageResponse(list, bannerList.length, page, pageSize)
})

// 分类列表
Mock.mock(/\/api\/service\/category(?!\/)/, 'get', (options) => {
  console.log('📂 分类列表API被调用:', options.url)
  const url = new URL('http://localhost' + options.url)
  const page = parseInt(url.searchParams.get('page') || '1')
  const pageSize = parseInt(url.searchParams.get('pageSize') || '10')

  const start = (page - 1) * pageSize
  const end = start + pageSize
  const list = categoryList.slice(start, end)

  return pageResponse(list, categoryList.length, page, pageSize)
})

// 分类树形结构接口
console.log('🔧 注册分类树API: /api/service/category/tree')
Mock.mock('/api/service/category/tree', 'get', () => {
  console.log('🌳 分类树API被调用')
  return successResponse(categoryTreeList)
})

// 分类推荐状态切换
Mock.mock(/\/api\/service\/category\/recommend\/(\d+)/, 'put', (options) => {
  const id = parseInt(options.url.match(/\/recommend\/(\d+)/)[1])
  const { is_recommend } = JSON.parse(options.body)
  const category = categoryList.find(item => item.id === id)

  if (category) {
    category.is_recommend = is_recommend
    category.update_time = Mock.Random.datetime()
    return successResponse(category, '推荐状态更新成功')
  } else {
    return errorResponse('分类不存在', 404)
  }
})

// 服务项目统计数据
console.log('🔧 注册统计API: /api/service/statistics')
Mock.mock('/api/service/statistics', 'get', () => {
  console.log('📊 统计API被调用')
  return successResponse({
    totalServices: serviceList.length,
    activeServices: serviceList.filter(item => item.status === 1).length,
    totalCategories: categoryList.length,
    totalBanners: bannerList.length,
    totalPoints: servicePointList.length,
    totalJingang: jingangList.length,
    totalConfigs: configList.length,
    monthlyOrders: Mock.Random.integer(100, 1000),
    monthlyRevenue: Mock.Random.float(10000, 100000, 2, 2),
    todayOrders: Mock.Random.integer(10, 100),
    todayRevenue: Mock.Random.float(1000, 10000, 2, 2)
  })
})

// 金刚区列表
Mock.mock(/\/api\/service\/jingang/, 'get', (options) => {
  console.log('💎 金刚区列表API被调用:', options.url)
  const url = new URL('http://localhost' + options.url)
  const page = parseInt(url.searchParams.get('page') || '1')
  const pageSize = parseInt(url.searchParams.get('pageSize') || '10')

  const start = (page - 1) * pageSize
  const end = start + pageSize
  const list = jingangList.slice(start, end)

  return pageResponse(list, jingangList.length, page, pageSize)
})

// 金刚区新增
Mock.mock(/\/api\/service\/jingang\/add$/, 'post', (options) => {
  console.log('➕ 金刚区新增API被调用:', options.body)
  const newItem = JSON.parse(options.body)
  newItem.id = jingangList.length + 1
  newItem.create_time = Mock.Random.datetime()
  newItem.update_time = Mock.Random.datetime()
  jingangList.push(newItem)
  return successResponse(newItem, '新增成功')
})

// 金刚区排序
Mock.mock('/api/service/jingang/sort', 'put', (options) => {
  console.log('🔄 金刚区排序API被调用:', options.body)
  const { sortData } = JSON.parse(options.body)

  sortData.forEach(item => {
    const index = jingangList.findIndex(j => j.id === item.id)
    if (index !== -1) {
      jingangList[index].sort = item.sort
      jingangList[index].position = item.position
      jingangList[index].update_time = Mock.Random.datetime()
    }
  })

  return successResponse(null, '排序更新成功')
})

// 服务点列表
Mock.mock(/\/api\/service\/point/, 'get', (options) => {
  console.log('📍 服务点列表API被调用:', options.url)
  const url = new URL('http://localhost' + options.url)
  const page = parseInt(url.searchParams.get('page') || '1')
  const pageSize = parseInt(url.searchParams.get('pageSize') || '10')

  const start = (page - 1) * pageSize
  const end = start + pageSize
  const list = servicePointList.slice(start, end)

  return pageResponse(list, servicePointList.length, page, pageSize)
})

// 服务点新增
Mock.mock(/\/api\/service\/point\/add$/, 'post', (options) => {
  console.log('➕ 服务点新增API被调用:', options.body)
  const newItem = JSON.parse(options.body)
  newItem.id = servicePointList.length + 1
  newItem.create_time = Mock.Random.datetime()
  newItem.update_time = Mock.Random.datetime()
  servicePointList.push(newItem)
  return successResponse(newItem, '新增成功')
})

// 服务点批量状态更新
Mock.mock('/api/service/point/batch-status', 'put', (options) => {
  console.log('🔄 服务点批量状态更新API被调用:', options.body)
  const { ids, status } = JSON.parse(options.body)

  let updatedCount = 0
  ids.forEach(id => {
    const index = servicePointList.findIndex(item => item.id === id)
    if (index !== -1) {
      servicePointList[index].status = status
      servicePointList[index].update_time = Mock.Random.datetime()
      updatedCount++
    }
  })

  return successResponse({ updatedCount }, `批量更新成功，共更新${updatedCount}条记录`)
})

// 配置列表
Mock.mock(/\/api\/service\/config(?!\/groups)/, 'get', (options) => {
  console.log('⚙️ 配置列表API被调用:', options.url)
  const url = new URL('http://localhost' + options.url)
  const page = parseInt(url.searchParams.get('page') || '1')
  const pageSize = parseInt(url.searchParams.get('pageSize') || '10')

  const start = (page - 1) * pageSize
  const end = start + pageSize
  const list = configList.slice(start, end)

  return pageResponse(list, configList.length, page, pageSize)
})

// 配置分组
Mock.mock('/api/service/config/groups', 'get', () => {
  console.log('📋 配置分组API被调用')
  const groups = [...new Set(configList.map(item => item.group))]
  return successResponse(groups)
})

// 配置批量更新
Mock.mock('/api/service/config/batch', 'put', (options) => {
  console.log('🔄 配置批量更新API被调用:', options.body)
  const { configs } = JSON.parse(options.body)

  let updatedCount = 0
  configs.forEach(config => {
    const index = configList.findIndex(item => item.id === config.id)
    if (index !== -1) {
      configList[index].value = config.value
      configList[index].update_time = Mock.Random.datetime()
      updatedCount++
    }
  })

  return successResponse({ updatedCount }, `批量更新成功，共更新${updatedCount}条配置`)
})

// 轮播图点击统计
Mock.mock(/\/api\/service\/banner\/click\/(\d+)/, 'post', (options) => {
  const id = parseInt(options.url.match(/\/click\/(\d+)/)[1])
  const banner = bannerList.find(item => item.id === id)

  if (banner) {
    banner.click_count = (banner.click_count || 0) + 1
    return successResponse(banner, '点击统计成功')
  } else {
    return errorResponse('轮播图不存在', 404)
  }
})

// 金刚区排序更新
Mock.mock('/api/service/jingang/sort', 'put', (options) => {
  const { sortData } = JSON.parse(options.body)

  sortData.forEach(item => {
    const jingang = jingangList.find(j => j.id === item.id)
    if (jingang) {
      jingang.sort = item.sort
      jingang.position = item.position
      jingang.update_time = Mock.Random.datetime()
    }
  })

  return successResponse(null, '排序更新成功')
})

// 服务点状态批量更新
Mock.mock('/api/service/point/batch-status', 'put', (options) => {
  const { ids, status } = JSON.parse(options.body)

  ids.forEach(id => {
    const point = servicePointList.find(item => item.id === id)
    if (point) {
      point.status = status
      point.update_time = Mock.Random.datetime()
    }
  })

  return successResponse(null, `批量${status === 1 ? '启用' : '禁用'}成功`)
})

// 配置分组获取
Mock.mock('/api/service/config/groups', 'get', () => {
  const groups = [...new Set(configList.map(item => item.group))]
  return successResponse(groups)
})

// 配置批量更新
Mock.mock('/api/service/config/batch', 'put', (options) => {
  const { configs } = JSON.parse(options.body)

  configs.forEach(configData => {
    const config = configList.find(item => item.id === configData.id)
    if (config) {
      Object.assign(config, configData, {
        update_time: Mock.Random.datetime()
      })
    }
  })

  return successResponse(null, '批量更新成功')
})

// 服务项目状态切换
Mock.mock(/\/api\/service\/status\/(\d+)/, 'put', (options) => {
  const id = parseInt(options.url.match(/\/status\/(\d+)/)[1])
  const { status } = JSON.parse(options.body)
  const service = serviceList.find(item => item.id === id)
  
  if (service) {
    service.status = status
    service.updateTime = Mock.Random.datetime()
    return successResponse(service, '状态更新成功')
  } else {
    return errorResponse('服务项目不存在', 404)
  }
})

// 批量操作
Mock.mock('/api/service/batch', 'post', (options) => {
  const { action, ids } = JSON.parse(options.body)
  
  switch (action) {
    case 'delete':
      ids.forEach(id => {
        const index = serviceList.findIndex(item => item.id === id)
        if (index !== -1) {
          serviceList.splice(index, 1)
        }
      })
      return successResponse(null, `批量删除${ids.length}条记录成功`)
      
    case 'enable':
      ids.forEach(id => {
        const service = serviceList.find(item => item.id === id)
        if (service) {
          service.status = 1
          service.updateTime = Mock.Random.datetime()
        }
      })
      return successResponse(null, `批量启用${ids.length}条记录成功`)
      
    case 'disable':
      ids.forEach(id => {
        const service = serviceList.find(item => item.id === id)
        if (service) {
          service.status = 0
          service.updateTime = Mock.Random.datetime()
        }
      })
      return successResponse(null, `批量禁用${ids.length}条记录成功`)
      
    default:
      return errorResponse('不支持的操作类型')
  }
})

// 轮播图新增
Mock.mock(/\/api\/service\/banner\/add$/, 'post', (options) => {
  console.log('➕ 轮播图新增API被调用:', options.body)
  const newItem = JSON.parse(options.body)
  newItem.id = bannerList.length + 1
  newItem.create_time = Mock.Random.datetime()
  newItem.update_time = Mock.Random.datetime()
  bannerList.push(newItem)
  return successResponse(newItem, '新增成功')
})

// 轮播图更新
Mock.mock(/\/api\/service\/banner\/update\/\d+/, 'put', (options) => {
  console.log('✏️ 轮播图更新API被调用:', options.url, options.body)
  const id = parseInt(options.url.match(/\/update\/(\d+)/)[1])
  const updateData = JSON.parse(options.body)
  const index = bannerList.findIndex(item => item.id === id)

  if (index !== -1) {
    bannerList[index] = { ...bannerList[index], ...updateData, update_time: Mock.Random.datetime() }
    return successResponse(bannerList[index], '更新成功')
  } else {
    return errorResponse('数据不存在', 404)
  }
})

// 轮播图删除
Mock.mock(/\/api\/service\/banner\/delete\/\d+/, 'delete', (options) => {
  console.log('🗑️ 轮播图删除API被调用:', options.url)
  const id = parseInt(options.url.match(/\/delete\/(\d+)/)[1])
  const index = bannerList.findIndex(item => item.id === id)

  if (index !== -1) {
    bannerList.splice(index, 1)
    return successResponse(null, '删除成功')
  } else {
    return errorResponse('数据不存在', 404)
  }
})

// 轮播图点击统计
Mock.mock(/\/api\/service\/banner\/click\/\d+/, 'post', (options) => {
  console.log('👆 轮播图点击统计API被调用:', options.url)
  const id = parseInt(options.url.match(/\/click\/(\d+)/)[1])
  const banner = bannerList.find(item => item.id === id)

  if (banner) {
    banner.clickCount = (banner.clickCount || 0) + 1
    return successResponse({ clickCount: banner.clickCount }, '点击统计成功')
  } else {
    return errorResponse('轮播图不存在', 404)
  }
})

// 分类新增
Mock.mock(/\/api\/service\/category\/add$/, 'post', (options) => {
  console.log('➕ 分类新增API被调用:', options.body)
  const newItem = JSON.parse(options.body)
  newItem.id = categoryList.length + 1
  newItem.create_time = Mock.Random.datetime()
  newItem.update_time = Mock.Random.datetime()
  categoryList.push(newItem)
  return successResponse(newItem, '新增成功')
})

// 分类推荐切换
Mock.mock(/\/api\/service\/category\/recommend\/\d+/, 'put', (options) => {
  console.log('⭐ 分类推荐切换API被调用:', options.url, options.body)
  const id = parseInt(options.url.match(/\/recommend\/(\d+)/)[1])
  const { is_recommend } = JSON.parse(options.body)
  const index = categoryList.findIndex(item => item.id === id)

  if (index !== -1) {
    categoryList[index].is_recommend = is_recommend
    categoryList[index].update_time = Mock.Random.datetime()
    return successResponse(categoryList[index], '推荐状态更新成功')
  } else {
    return errorResponse('分类不存在', 404)
  }
})

// ==================== API-V2版本路径支持 ====================
// 为了支持新的API-V2路径，添加对应的Mock处理器

console.log('🔧 开始注册API-V2版本路径...')

// 服务管理 - API-V2路径
Mock.mock(/\/api\/admin\/service\/list/, 'get', (options) => {
  console.log('📋 服务列表API-V2被调用:', options.url)
  const url = new URL('http://localhost' + options.url)
  const pageNum = parseInt(url.searchParams.get('pageNum') || '1')
  const pageSize = parseInt(url.searchParams.get('pageSize') || '10')
  const title = url.searchParams.get('title') || ''
  const status = url.searchParams.get('status')
  const categoryId = url.searchParams.get('categoryId')

  let filteredData = [...serviceList]

  // 标题过滤
  if (title) {
    filteredData = filteredData.filter(item =>
      item.title.toLowerCase().includes(title.toLowerCase())
    )
  }

  // 状态过滤
  if (status !== null && status !== '') {
    filteredData = filteredData.filter(item => item.status == status)
  }

  // 分类过滤
  if (categoryId !== null && categoryId !== '') {
    filteredData = filteredData.filter(item => item.categoryId == categoryId)
  }

  const start = (pageNum - 1) * pageSize
  const end = start + pageSize
  const list = filteredData.slice(start, end)

  return {
    success: true,
    code: '200',
    message: '获取成功',
    data: {
      list: list,
      totalCount: filteredData.length,
      totalPage: Math.ceil(filteredData.length / pageSize),
      pageNum: pageNum,
      pageSize: pageSize
    }
  }
})

// 轮播图管理 - API-V2路径
Mock.mock(/\/api\/admin\/banner\/list/, 'get', (options) => {
  console.log('📋 轮播图列表API-V2被调用:', options.url)
  const url = new URL('http://localhost' + options.url)
  const pageNum = parseInt(url.searchParams.get('pageNum') || '1')
  const pageSize = parseInt(url.searchParams.get('pageSize') || '10')
  const status = url.searchParams.get('status')
  const type = url.searchParams.get('type')

  let filteredData = [...bannerList]

  // 状态过滤
  if (status !== null && status !== '') {
    filteredData = filteredData.filter(item => item.status == status)
  }

  // 类型过滤
  if (type !== null && type !== '') {
    filteredData = filteredData.filter(item => item.type == type)
  }

  const start = (pageNum - 1) * pageSize
  const end = start + pageSize
  const list = filteredData.slice(start, end)

  return {
    success: true,
    code: '200',
    message: '获取成功',
    data: {
      list: list,
      totalCount: filteredData.length,
      totalPage: Math.ceil(filteredData.length / pageSize),
      pageNum: pageNum,
      pageSize: pageSize
    }
  }
})

// 金刚区管理 - API-V2路径
Mock.mock(/\/api\/admin\/nav\/list/, 'get', (options) => {
  console.log('📋 金刚区列表API-V2被调用:', options.url)
  const url = new URL('http://localhost' + options.url)
  const pageNum = parseInt(url.searchParams.get('pageNum') || '1')
  const pageSize = parseInt(url.searchParams.get('pageSize') || '10')
  const status = url.searchParams.get('status')
  const type = url.searchParams.get('type')

  let filteredData = [...jingangList]

  // 状态过滤
  if (status !== null && status !== '') {
    filteredData = filteredData.filter(item => item.status == status)
  }

  // 类型过滤
  if (type !== null && type !== '') {
    filteredData = filteredData.filter(item => item.type == type)
  }

  const start = (pageNum - 1) * pageSize
  const end = start + pageSize
  const list = filteredData.slice(start, end)

  return {
    success: true,
    code: '200',
    message: '获取成功',
    data: {
      list: list,
      totalCount: filteredData.length,
      totalPage: Math.ceil(filteredData.length / pageSize),
      pageNum: pageNum,
      pageSize: pageSize
    }
  }
})

// 服务点管理 - API-V2路径
Mock.mock(/\/api\/admin\/agent\/list/, 'get', (options) => {
  console.log('📋 服务点列表API-V2被调用:', options.url)
  const url = new URL('http://localhost' + options.url)
  const pageNum = parseInt(url.searchParams.get('pageNum') || '1')
  const pageSize = parseInt(url.searchParams.get('pageSize') || '10')
  const status = url.searchParams.get('status')
  const name = url.searchParams.get('name')
  const address = url.searchParams.get('address')

  let filteredData = [...servicePointList]

  // 状态过滤
  if (status !== null && status !== '') {
    filteredData = filteredData.filter(item => item.status == status)
  }

  // 名称过滤
  if (name) {
    filteredData = filteredData.filter(item =>
      item.name.toLowerCase().includes(name.toLowerCase())
    )
  }

  // 地址过滤
  if (address) {
    filteredData = filteredData.filter(item =>
      item.address.toLowerCase().includes(address.toLowerCase())
    )
  }

  const start = (pageNum - 1) * pageSize
  const end = start + pageSize
  const list = filteredData.slice(start, end)

  return {
    success: true,
    code: '200',
    message: '获取成功',
    data: {
      list: list,
      totalCount: filteredData.length,
      totalPage: Math.ceil(filteredData.length / pageSize),
      pageNum: pageNum,
      pageSize: pageSize
    }
  }
})

// ==================== 一口价管理Mock数据 ====================

const onepriceList = Mock.mock({
  'list|15-25': [{
    'id|+1': 1,
    'serviceId|500-600': 517, // 添加服务ID字段
    'serviceTitle': '@pick(["马桶安装", "水龙头维修", "下水道疏通", "热水器安装", "空调维修", "洗衣机维修"])',
    'regionName': function() {
      const regions = [
        '安徽省',
        '安徽省-阜阳市-太和县',
        '安徽省-阜阳市-颍泉区',
        '安徽省-阜阳市-临泉县',
        '北京市-东城区',
        '上海市-浦东新区',
        '广东省-深圳市-南山区',
        '全国'
      ]
      return Mock.Random.pick(regions)
    },
    // 添加地区ID字段，根据regionName生成对应的ID
    'provinceId': function() {
      const regionName = this.regionName
      if (regionName === '全国') return null
      if (regionName.includes('安徽省')) return 1001
      if (regionName.includes('北京市')) return 1002
      if (regionName.includes('上海市')) return 1003
      if (regionName.includes('广东省')) return 1004
      return Mock.Random.integer(1000, 2000)
    },
    'cityId': function() {
      const regionName = this.regionName
      if (regionName === '全国' || !regionName.includes('-')) return null
      if (regionName.includes('阜阳市')) return 2001
      if (regionName.includes('东城区')) return 2002
      if (regionName.includes('浦东新区')) return 2003
      if (regionName.includes('深圳市')) return 2004
      return Mock.Random.integer(2000, 3000)
    },
    'areaId': function() {
      const regionName = this.regionName
      const parts = regionName.split('-')
      if (parts.length < 3) return null
      if (regionName.includes('太和县')) return 3001
      if (regionName.includes('颍泉区')) return 3002
      if (regionName.includes('临泉县')) return 3003
      if (regionName.includes('南山区')) return 3004
      return Mock.Random.integer(3000, 4000)
    },
    'num|1-5': 1,
    'originalPrice|150-600.2': 1, // 新增原价字段
    'price|100-500.2': 1,
    'status|1': [1, -1],
    'priority|1-20': 1,
    'effectiveBegin': '@datetime("yyyy-MM-dd HH:mm:ss")',
    'effectiveEnd': '@datetime("yyyy-MM-dd HH:mm:ss")',
    'configs': function() {
      return Mock.mock({
        'list|1-3': [{
          'settingId|1000-2000': 1,
          'problemDesc': '@pick(["马桶安装多选", "马桶安装单选", "水管材质", "上门时间"])',
          'values|1-3': ['@pick(["多选1", "多选2", "多选3", "单选1", "单选2"])']
        }]
      }).list
    },
    'remark': '@cparagraph(1, 2)',
    'createTime': '@datetime("yyyy-MM-dd HH:mm:ss")',
    'updateTime': '@datetime("yyyy-MM-dd HH:mm:ss")'
  }]
}).list

// 一口价列表API
Mock.mock(/\/api\/admin\/oneprice\/list/, 'get', (options) => {
  console.log('📋 一口价列表Mock API被调用')
  const url = new URL(options.url, 'http://localhost')
  const pageNum = parseInt(url.searchParams.get('pageNum') || '1')
  const pageSize = parseInt(url.searchParams.get('pageSize') || '10')
  const serviceTitle = url.searchParams.get('serviceTitle')
  const status = url.searchParams.get('status')

  let filteredData = [...onepriceList]

  // 项目名称过滤
  if (serviceTitle) {
    filteredData = filteredData.filter(item =>
      item.serviceTitle.includes(serviceTitle)
    )
  }

  // 状态过滤
  if (status !== null && status !== '') {
    filteredData = filteredData.filter(item => item.status == status)
  }

  const start = (pageNum - 1) * pageSize
  const end = start + pageSize
  const list = filteredData.slice(start, end)

  return successResponse({
    list: list,
    totalCount: filteredData.length,
    totalPage: Math.ceil(filteredData.length / pageSize),
    pageNum: pageNum,
    pageSize: pageSize
  })
})

// 一口价详情API
Mock.mock(/\/api\/admin\/oneprice\/info\/(\d+)/, 'get', (options) => {
  console.log('🔍 一口价详情Mock API被调用')
  const id = parseInt(options.url.match(/\/(\d+)$/)[1])
  const item = onepriceList.find(item => item.id === id)

  if (item) {
    return successResponse(item)
  } else {
    return {
      success: false,
      code: '404',
      message: '一口价配置不存在'
    }
  }
})

// 一口价新增API
Mock.mock(/\/api\/admin\/oneprice\/add/, 'post', (options) => {
  console.log('➕ 一口价新增Mock API被调用')
  const body = JSON.parse(options.body)

  const newItem = {
    id: onepriceList.length + 1,
    serviceTitle: '新增服务',
    regionName: '新增地区',
    ...body,
    createTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
    updateTime: new Date().toISOString().replace('T', ' ').substring(0, 19)
  }

  onepriceList.push(newItem)
  return successResponse(newItem, '新增成功')
})

// 一口价修改API
Mock.mock(/\/api\/admin\/oneprice\/update/, 'post', (options) => {
  console.log('✏️ 一口价修改Mock API被调用')
  const body = JSON.parse(options.body)
  const index = onepriceList.findIndex(item => item.id === body.id)

  if (index !== -1) {
    onepriceList[index] = {
      ...onepriceList[index],
      ...body,
      updateTime: new Date().toISOString().replace('T', ' ').substring(0, 19)
    }
    return successResponse(onepriceList[index], '修改成功')
  } else {
    return {
      success: false,
      code: '404',
      message: '一口价配置不存在'
    }
  }
})

// 一口价删除API
Mock.mock(/\/api\/admin\/oneprice\/delete\/(\d+)/, 'post', (options) => {
  console.log('🗑️ 一口价删除Mock API被调用')
  const id = parseInt(options.url.match(/\/(\d+)$/)[1])
  const index = onepriceList.findIndex(item => item.id === id)

  if (index !== -1) {
    onepriceList.splice(index, 1)
    return successResponse(null, '删除成功')
  } else {
    return {
      success: false,
      code: '404',
      message: '一口价配置不存在'
    }
  }
})

// 一口价状态切换API
Mock.mock(/\/api\/admin\/oneprice\/toggleStatus/, 'post', (options) => {
  console.log('🔄 一口价状态切换Mock API被调用')
  const body = JSON.parse(options.body)
  const index = onepriceList.findIndex(item => item.id === body.id)

  if (index !== -1) {
    onepriceList[index].status = body.status || (onepriceList[index].status === 1 ? -1 : 1)
    onepriceList[index].updateTime = new Date().toISOString().replace('T', ' ').substring(0, 19)
    return successResponse(onepriceList[index], '状态更新成功')
  } else {
    return {
      success: false,
      code: '404',
      message: '一口价配置不存在'
    }
  }
})

// ==================== 一口价配置项Mock数据 ====================

// 模拟服务配置项数据
const serviceConfigList = Mock.mock({
  'list|6': [{
    'id|+1': 1147,
    'type': 0,
    'serviceId': 517,
    'problemDesc': function() {
      const configs = ['马桶安装多选', '马桶安装单选', '水管材质', '上门时间', '服务范围', '特殊要求']
      return configs[this.id - 1147] || `配置项${this.id}`
    },
    'problemContent': function() {
      const descriptions = ['测试马桶安装多选描述', '测试马桶安装单选描述', '水管材质选择', '选择上门服务时间', '服务的具体范围', '其他特殊需求说明']
      return descriptions[this.id - 1147] || `配置项${this.id}的详细描述`
    },
    'isRequired': function() {
      return this.id <= 1148 ? 1 : Mock.Random.pick([0, 1])
    },
    'inputType': function() {
      const types = [4, 3, 3, 3, 4, 1] // 4-多选，3-单选，1-输入框
      return types[this.id - 1147] || 3
    },
    'val': null,
    'createTime': '@datetime("yyyy-MM-dd HH:mm:ss")',
    'options': function() {
      if (this.inputType === 3 || this.inputType === 4) {
        const optionsList = {
          1147: ['多选1', '多选2', '多选3', '多选4', '多选5'],
          1148: ['单选1', '单选2', '单选3'],
          1149: ['PPR管', '铜管', '铁管'],
          1150: ['上午', '下午', '晚上'],
          1151: ['室内', '室外', '全屋'],
          1152: ['高空作业', '特殊环境', '加急服务']
        }
        return JSON.stringify(optionsList[this.id] || ['选项1', '选项2', '选项3'])
      }
      return null
    }
  }]
}).list

// 获取服务配置项列表API
Mock.mock(/\/api\/admin\/oneprice\/priceSetting\/list/, 'get', (options) => {
  console.log('🔍 获取服务配置项Mock API被调用')
  const url = new URL(options.url, 'http://localhost')
  const serviceId = url.searchParams.get('serviceId')
  
  if (!serviceId) {
    return {
      success: false,
      code: '400',
      message: '服务ID不能为空'
    }
  }
  
  // 返回配置项数据
  return successResponse(serviceConfigList)
})

console.log('✅ 服务项目模块Mock数据已加载')
console.log('📊 数据统计:')
console.log(`   - 服务项目: ${serviceList.length} 条`)
console.log(`   - 分类: ${categoryList.length} 条`)
console.log(`   - 轮播图: ${bannerList.length} 条`)
console.log(`   - 金刚区: ${jingangList.length} 条`)
console.log(`   - 服务点: ${servicePointList.length} 条`)
console.log(`   - 配置: ${configList.length} 条`)
console.log(`   - 一口价: ${onepriceList.length} 条`)
console.log(`   - 服务配置项: ${serviceConfigList.length} 条`)
console.log('🎯 所有API已注册完成，包含API-V2路径支持')

export { serviceList, categoryList, bannerList, jingangList, servicePointList, configList, onepriceList, serviceConfigList }
