<!--
  一口价管理页面
  基于快速开发指南和API接口规范开发
  API: /api/admin/oneprice/list
-->
<template>
  <div class="service-oneprice">
    <!-- 顶部导航 -->
    <TopNav title="一口价管理" />

    <div class="content-container">
      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="项目名称" prop="serviceTitle">
                <el-input
                  size="default"
                  v-model="searchForm.serviceTitle"
                  placeholder="请输入项目名称"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>
              <el-form-item label="状态" prop="status">
                <el-select
                  size="default"
                  v-model="searchForm.status"
                  placeholder="请选择状态"
                  clearable
                  style="width: 120px"
                >
                  <el-option label="可用" :value="1" />
                  <el-option label="不可用" :value="-1" />
                </el-select>
              </el-form-item>
              <el-form-item label="地区" prop="regionPath">
                <el-cascader
                  size="default"
                  v-model="searchForm.regionPath"
                  :options="regionOptions"
                  :props="cascaderProps"
                  placeholder="请选择省市区"
                  clearable
                  style="width: 300px"
                  @change="handleRegionChange"
                />
              </el-form-item>
              <el-form-item>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Search"
                  @click="handleSearch"
                >
                  搜索
                </LbButton>
                <LbButton
                  size="default"
                  icon="RefreshLeft"
                  @click="handleReset"
                >
                  重置
                </LbButton>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Plus"
                  @click="handleAdd"
                >
                  新增一口价
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontSize: '16px', fontWeight: '600' }"
          :cell-style="{ fontSize: '14px', padding: '12px 8px' }"
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" align="center" />

          <el-table-column label="项目名称" min-width="150">
            <template #default="scope">
              {{ scope.row.serviceTitle || scope.row.serviceName || '-' }}
            </template>
          </el-table-column>

          <el-table-column label="地区" min-width="200">
            <template #default="scope">
              <div>
                <div v-if="scope.row.regionName">{{ scope.row.regionName }}</div>
                <div v-if="scope.row.cityName" style="color: #909399; font-size: 12px;">{{ scope.row.cityName }}</div>
                <span v-if="!scope.row.regionName && !scope.row.cityName">-</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="originalPrice" label="原价" width="120" align="center">
            <template #default="scope">
              <span v-if="scope.row.originalPrice !== null && scope.row.originalPrice !== undefined" style="color: #909399; font-weight: 500;">
                ¥{{ Number(scope.row.originalPrice).toFixed(2) }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column prop="price" label="价格" width="120" align="center">
            <template #default="scope">
              <span style="color: #f56c6c; font-weight: 500;">
                ¥{{ scope.row.price }}
              </span>
            </template>
          </el-table-column>

          <el-table-column prop="num" label="数量" width="80" align="center" />

          <el-table-column prop="priority" label="优先级" width="100" align="center" />

          <el-table-column label="配置项" min-width="250">
            <template #default="scope">
              <div v-if="scope.row.configs && scope.row.configs.length > 0" class="configs-display">
                <div v-for="(config, index) in scope.row.configs" :key="index" class="config-item-display">
                  <el-tag size="small" type="info" style="margin-right: 5px; margin-bottom: 3px;">
                    {{ config.problemDesc || `配置项${config.settingId}` }}: {{ Array.isArray(config.values) ? config.values.join(', ') : config.values }}
                  </el-tag>
                </div>
              </div>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>

          <el-table-column label="生效时间" width="180">
            <template #default="scope">
              <div class="time-column">
                <p>{{ formatDate(scope.row.effectiveBegin) }}</p>
                <p>{{ formatDate(scope.row.effectiveEnd) }}</p>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="120" align="center">
            <template #default="scope">
              <el-tag
                :type="scope.row.status === 1 ? 'success' : 'danger'"
                style="cursor: pointer"
                @click="handleStatusToggle(scope.row)"
              >
                {{ scope.row.status === 1 ? '可用' : '不可用' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <div class="table-operate">
                <LbButton
                  size="default"
                  type="primary"
                  @click="handleEdit(scope.row)"
                >
                  编辑
                </LbButton>
                <LbButton
                  size="default"
                  type="danger"
                  @click="handleDelete(scope.row)"
                >
                  删除
                </LbButton>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="服务项目" prop="serviceId">
              <el-select
                v-model="form.serviceId"
                placeholder="请选择或搜索服务项目"
                style="width: 100%"
                filterable
                remote
                :remote-method="searchServices"
                :loading="serviceSearchLoading"
                :disabled="isEdit"
                @change="handleServiceChange"
                @focus="handleServiceFocus"
                clearable
              >
                <el-option
                  v-for="service in serviceList"
                  :key="service.id"
                  :label="service.title"
                  :value="service.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="原价" prop="originalPrice">
              <el-input-number
                v-model="form.originalPrice"
                :min="0"
                :precision="2"
                placeholder="请输入原价"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="价格" prop="price">
              <el-input-number
                v-model="form.price"
                :min="0"
                :precision="2"
                placeholder="请输入价格"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数量" prop="num">
              <el-input-number
                v-model="form.num"
                :min="1"
                placeholder="请输入数量"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="地区" prop="regionPath">
          <el-cascader
            v-model="form.regionPath"
            :options="regionOptions"
            :props="cascaderProps"
            placeholder="请选择省市区（可选）"
            style="width: 100%"
            clearable
            @change="handleFormRegionChange"
            :show-all-levels="false"
            separator=" > "
          />
          <div class="region-tips">
            <p>地区选择说明：可选择省份、城市或区县，留空表示全国通用</p>
            <p v-if="form.regionPath && form.regionPath.length > 0" style="color: #409eff;">
              当前选择：{{ getRegionDisplayName() }}
            </p>
          </div>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-input-number
                v-model="form.priority"
                :min="0"
                placeholder="请输入优先级"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :value="1">可用</el-radio>
                <el-radio :value="-1">不可用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生效开始时间" prop="effectiveBegin">
              <el-date-picker
                v-model="form.effectiveBegin"
                type="datetime"
                placeholder="选择开始时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效结束时间" prop="effectiveEnd">
              <el-date-picker
                v-model="form.effectiveEnd"
                type="datetime"
                placeholder="选择结束时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="配置项" prop="configs">
          <div class="configs-container">
            <div
              v-for="(config, index) in form.configs"
              :key="`config-${config.settingId}-${index}`"
              class="config-item"
            >
              <el-row :gutter="10">
                <el-col :span="8">
                  <div class="config-info">
                    <p class="config-label">{{ config.problemDesc || '配置项' + (index + 1) }}</p>
                    <p class="config-desc">{{ getInputTypeText(config.inputType) }}</p>
                    <p class="config-desc" v-if="config.isRequired === 1" style="color: #f56c6c;">必填</p>
                  </div>
                </el-col>
                <el-col :span="14">
                  <el-select
                    :model-value="config.inputType === 4 ? config.values : (config.values && config.values.length > 0 ? config.values[0] : '')"
                    :multiple="config.inputType === 4"
                    :placeholder="config.inputType === 4 ? '请选择配置值（多选）' : '请选择配置值（单选）'"
                    style="width: 100%"
                    clearable
                    @update:model-value="handleConfigValueChange(config, $event)"
                  >
                    <el-option
                      v-for="option in config.optionsList"
                      :key="option"
                      :label="option"
                      :value="option"
                    />
                  </el-select>
                </el-col>
                <el-col :span="2">
                  <el-button
                    type="danger"
                    size="small"
                    @click="removeConfig(index)"
                    icon="Delete"
                    title="删除此配置项"
                    :disabled="form.configs.length <= 1 && config.isRequired === 1"
                  />
                </el-col>
              </el-row>
            </div>
            <div v-if="form.configs.length === 0" class="no-configs">
              请选择服务项目后自动加载配置项
            </div>
          </div>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="dialogVisible = false">取消</LbButton>
          <LbButton type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </LbButton>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'

// 直接导入API
import { api } from '@/api-v2'

// 开发环境下导入测试功能


// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const tableData = ref([])
const total = ref(0)
const searchFormRef = ref()
const formRef = ref()

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const currentEditId = ref(null)

// 地区数据
const regionOptions = ref([])
const cascaderProps = {
  value: 'id',
  label: 'trueName',
  children: 'children',
  multiple: false,
  emitPath: true,
  checkStrictly: false,
  expandTrigger: 'hover'
}

// 服务列表
const serviceList = ref([])
const serviceSearchLoading = ref(false)


// 防止频繁调用的标志
const isStatusChanging = ref(false)

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10,
  serviceTitle: '',
  status: null,
  regionPath: [],
  provinceId: null,
  cityId: null,
  areaId: null
})

// 表单数据
const form = reactive({
  serviceId: null,
  regionPath: [],
  provinceId: null,
  cityId: null,
  areaId: null,
  originalPrice: null, // 新增原价字段
  price: null,
  status: 1,
  priority: 0,
  num: 1,
  effectiveBegin: '',
  effectiveEnd: '',
  configs: [],
  remark: ''
})

// 表单验证规则
const rules = reactive({
  serviceId: [
    { required: true, message: '请选择服务项目', trigger: 'change' }
  ],
  originalPrice: [
    { type: 'number', min: 0, message: '原价不能小于0', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
  ],
  num: [
    { required: true, message: '请输入数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '数量不能小于1', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请输入优先级', trigger: 'blur' },
    { type: 'number', min: 0, message: '优先级不能小于0', trigger: 'blur' }
  ],
  effectiveBegin: [
    { required: true, message: '请选择生效开始时间', trigger: 'change' }
  ],
  effectiveEnd: [
    { required: true, message: '请选择生效结束时间', trigger: 'change' }
  ]
})

// 获取一口价列表数据
const getTableDataList = async (flag) => {
  if (flag) searchForm.pageNum = 1

  loading.value = true
  try {
    // 构建查询参数
    const params = {
      pageNum: searchForm.pageNum,
      pageSize: searchForm.pageSize
    }

    // 添加可选参数
    if (searchForm.serviceTitle) params.serviceTitle = searchForm.serviceTitle
    if (searchForm.status !== null && searchForm.status !== '') params.status = searchForm.status
    if (searchForm.provinceId !== null && searchForm.provinceId !== '') params.provinceId = searchForm.provinceId
    if (searchForm.cityId !== null && searchForm.cityId !== '') params.cityId = searchForm.cityId
    if (searchForm.areaId !== null && searchForm.areaId !== '') params.areaId = searchForm.areaId

    // 使用API-V2调用方式
    const result = await api.service.onepriceList(params)
    console.log('📋 一口价列表数据 (API-V2):', result)

    // 处理API响应格式
    console.log('🔍 检查API响应:', {
      code: result.code,
      codeType: typeof result.code,
      message: result.message,
      dataExists: !!result.data
    })

    if (result.code === '200' || result.code === 200) {
      const data = result.data
      // 设置表格数据
      tableData.value = data.list || []
      total.value = data.totalCount || data.total || 0

      console.log('📊 处理后的数据:', {
        list: tableData.value,
        total: total.value,
        pageNum: data.pageNum,
        pageSize: data.pageSize
      })
    } else {
      console.error('❌ API响应错误:', result)
      ElMessage.error(result.message || result.msg || result.meg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取一口价列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  getTableDataList(1)
}

// 重置处理
const handleReset = () => {
  searchForm.serviceTitle = ''
  searchForm.status = null
  searchForm.regionPath = []
  searchForm.provinceId = null
  searchForm.cityId = null
  searchForm.areaId = null
  searchFormRef.value?.resetFields()

  getTableDataList(1)
}

// 新增处理
const handleAdd = async () => {
  dialogTitle.value = '新增一口价'
  isEdit.value = false
  currentEditId.value = null
  resetForm()

  // 确保服务列表已加载
  if (serviceList.value.length === 0) {
    console.log('📋 新增一口价时加载服务列表')
    await loadServiceList()
  }

  dialogVisible.value = true
}

// 编辑处理
const handleEdit = async (row) => {
  try {
    dialogTitle.value = '编辑一口价'
    isEdit.value = true
    currentEditId.value = row.id

    console.log('📝 开始编辑一口价，ID:', row.id)

    // 确保服务列表已加载
    if (serviceList.value.length === 0) {
      await loadServiceList()
    }

  
    // 调用详情接口获取完整数据
    console.log('🔍 获取一口价详情数据，ID:', row.id)

    try {
      const detailResult = await api.service.onepriceInfo({ id: row.id })
      console.log('📡 详情接口响应:', detailResult)

      if (detailResult.code === '200' || detailResult.code === 200) {
        const detailData = detailResult.data
        console.log('📋 详情接口返回数据:', detailData)

        // 使用详情数据填充表单
        await fillFormWithDetailData(detailData)
      } else {
        console.warn('⚠️ 详情接口返回错误:', detailResult.message || detailResult.msg)
        ElMessage.warning(detailResult.message || detailResult.msg || '获取详情失败，使用列表数据')
        // 如果详情接口失败，使用列表数据
        await fillFormWithListData(row)
      }
    } catch (apiError) {
      console.error('❌ 详情接口调用异常:', apiError)
      ElMessage.warning('获取详情失败，使用列表数据')
      // 如果详情接口异常，使用列表数据
      await fillFormWithListData(row)
    }

    dialogVisible.value = true
  } catch (error) {
    console.error('编辑一口价失败:', error)
    ElMessage.error('编辑失败')
  }
}

// 使用详情数据填充表单
const fillFormWithDetailData = async (detailData) => {
  console.log('📝 使用详情数据填充表单，数据:', detailData)

  // 处理服务项目信息 - 支持 serviceTitle 和 serviceName 字段
  const serviceTitle = detailData.serviceTitle || detailData.serviceName
  const serviceId = detailData.serviceId

  console.log('🔍 服务项目信息:', {
    serviceId,
    serviceTitle,
    originalServiceTitle: detailData.serviceTitle,
    originalServiceName: detailData.serviceName
  })

  // 确保当前服务项目在列表中
  if (serviceId && serviceTitle) {
    const existingService = serviceList.value.find(service => service.id === serviceId)
    if (!existingService) {
      serviceList.value.unshift({
        id: serviceId,
        title: serviceTitle
      })
      console.log('✅ 已添加当前服务项目到列表:', { id: serviceId, title: serviceTitle })
    }
  }

  // 填充基本表单数据
  Object.assign(form, {
    serviceId: serviceId,
    provinceId: detailData.provinceId || null,
    cityId: detailData.cityId || null,
    areaId: detailData.areaId || null,
    originalPrice: detailData.originalPrice || null,
    price: detailData.price || 0,
    status: detailData.status || 1,
    priority: detailData.priority || 0,
    num: detailData.num || 1,
    effectiveBegin: detailData.effectiveBegin || '',
    effectiveEnd: detailData.effectiveEnd || '',
    configs: [], // 先置空，后面会重新加载
    remark: detailData.remark || ''
  })

  console.log('📊 基本表单数据填充完成:', {
    serviceId: form.serviceId,
    price: form.price,
    originalPrice: form.originalPrice,
    num: form.num,
    priority: form.priority,
    status: form.status,
    effectiveBegin: form.effectiveBegin,
    effectiveEnd: form.effectiveEnd,
    remark: form.remark
  })

  // 处理地区信息
  const regionPath = []
  if (detailData.provinceId !== null && detailData.provinceId !== undefined && detailData.provinceId !== '') {
    regionPath.push(detailData.provinceId)
  }
  if (detailData.cityId !== null && detailData.cityId !== undefined && detailData.cityId !== '') {
    regionPath.push(detailData.cityId)
  }
  if (detailData.areaId !== null && detailData.areaId !== undefined && detailData.areaId !== '') {
    regionPath.push(detailData.areaId)
  }
  form.regionPath = regionPath

  console.log('🗺️ 设置地区路径:')
  console.log('  - 详情数据: provinceId=' + detailData.provinceId + ', cityId=' + detailData.cityId + ', areaId=' + detailData.areaId)
  console.log('  - 路径数组:', regionPath)
  console.log('  - 地区显示名称:', detailData.regionName)

  // 处理配置项数据
  if (serviceId) {
    if (detailData.configs && Array.isArray(detailData.configs) && detailData.configs.length > 0) {
      console.log('📋 详情中的配置项数据:', detailData.configs)

      // 直接使用详情接口返回的配置项数据，不再调用配置项列表接口
      form.configs = detailData.configs.map(detailConfig => ({
        settingId: detailConfig.settingId,
        problemDesc: detailConfig.problemDesc || `配置项${detailConfig.settingId}`,
        problemContent: '',
        inputType: Array.isArray(detailConfig.values) && detailConfig.values.length > 1 ? 4 : 3, // 根据值数量推断类型
        isRequired: 1,
        values: Array.isArray(detailConfig.values) ? detailConfig.values : [detailConfig.values],
        optionsList: Array.isArray(detailConfig.values) ? detailConfig.values : [detailConfig.values] // 使用当前值作为选项
      }))

      console.log('✅ 直接使用详情配置项数据，共', form.configs.length, '个配置项')
    } else {
      // 如果没有配置项数据，加载默认模板
      console.log('📋 没有配置项数据，加载默认模板')
      await loadServiceConfigs(serviceId, false)
    }
  }

  console.log('📊 详情数据填充完成:', form)
}

// 使用列表数据填充表单
const fillFormWithListData = async (row) => {
  console.log('📝 使用列表数据填充表单，数据:', row)

  // 确保当前选中的服务项目在服务列表中 - 支持 serviceTitle 和 serviceName 字段
  const serviceTitle = row.serviceTitle || row.serviceName
  if (row.serviceId && serviceTitle) {
    const existingService = serviceList.value.find(service => service.id === row.serviceId)
    if (!existingService) {
      serviceList.value.unshift({
        id: row.serviceId,
        title: serviceTitle
      })
      console.log('✅ 已添加当前服务项目到列表:', { id: row.serviceId, title: serviceTitle })
    }
  }

  // 填充表单数据
  Object.assign(form, {
    serviceId: row.serviceId || null,
    provinceId: row.provinceId || null,
    cityId: row.cityId || null,
    areaId: row.areaId || null,
    originalPrice: row.originalPrice || null,
    price: row.price || 0,
    status: row.status || 1,
    priority: row.priority || 0,
    num: row.num || 1,
    effectiveBegin: row.effectiveBegin || '',
    effectiveEnd: row.effectiveEnd || '',
    configs: [],
    remark: row.remark || ''
  })

  // 设置地区路径
  const regionPath = []
  if (row.provinceId !== null && row.provinceId !== undefined && row.provinceId !== '') {
    regionPath.push(row.provinceId)
  }
  if (row.cityId !== null && row.cityId !== undefined && row.cityId !== '') {
    regionPath.push(row.cityId)
  }
  if (row.areaId !== null && row.areaId !== undefined && row.areaId !== '') {
    regionPath.push(row.areaId)
  }
  form.regionPath = regionPath

  // 处理配置项数据
  if (row.serviceId) {
    await loadServiceConfigs(row.serviceId, false)

    if (row.configs && Array.isArray(row.configs) && row.configs.length > 0) {
      row.configs.forEach(listConfig => {
        const configIndex = form.configs.findIndex(c => c.settingId === listConfig.settingId)
        if (configIndex !== -1) {
          // 确保values始终为数组格式
          form.configs[configIndex].values = Array.isArray(listConfig.values)
            ? listConfig.values
            : [listConfig.values]
          if (listConfig.problemDesc) {
            form.configs[configIndex].problemDesc = listConfig.problemDesc
          }
        }
      })
    }
  }

  console.log('📊 列表数据填充完成:', form)
}

// 删除处理
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个一口价配置吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await api.service.onepriceDelete({ id: row.id })

    if (result.code === '200' || result.code === 200) {
      ElMessage.success('删除成功')
      getTableDataList()
    } else {
      ElMessage.error(result.message || result.msg || result.meg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除一口价失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 状态切换处理 - 使用标签点击方式
const handleStatusToggle = async (row) => {
  // 防止频繁调用状态切换API
  if (isStatusChanging.value) {
    console.log('⚠️ 状态切换API正在调用中，跳过重复请求')
    return
  }

  // 设置防重复调用标志
  isStatusChanging.value = true

  try {
    // 只传 ID，后端自动切换状态
    const result = await api.service.onepriceToggleStatus({
      id: row.id
    })

    if (result.code === '200' || result.code === 200) {
      // 切换状态
      row.status = row.status === 1 ? -1 : 1
      ElMessage.success('状态更新成功')
      console.log(`✅ 一口价 ${row.id} 状态已更新为: ${row.status}`)
    } else {
      ElMessage.error(result.message || result.msg || result.meg || '状态更新失败')
    }
  } catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
  } finally {
    // 清除防重复调用标志
    isStatusChanging.value = false
  }
}

// 表单提交处理
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate()

    submitLoading.value = true

    const submitData = {
      serviceId: form.serviceId,
      originalPrice: form.originalPrice, // 新增原价字段
      price: form.price,
      status: form.status,
      priority: form.priority,
      num: form.num,
      effectiveBegin: form.effectiveBegin,
      effectiveEnd: form.effectiveEnd,
      configs: form.configs.filter(config =>
        config.values &&
        (Array.isArray(config.values) ? config.values.length > 0 : config.values !== '')
      ).map(config => ({
        settingId: config.settingId || config.id,
        values: Array.isArray(config.values) ? config.values : [config.values]
      })),
      remark: form.remark
    }

    // 添加地区信息（可选）
    if (form.provinceId) submitData.provinceId = form.provinceId
    if (form.cityId) submitData.cityId = form.cityId
    if (form.areaId) submitData.areaId = form.areaId

    console.log('📤 提交一口价数据:', submitData)

    let result
    if (isEdit.value) {
      // 编辑模式
      submitData.id = currentEditId.value
      console.log('✏️ 编辑一口价，ID:', currentEditId.value)
      result = await api.service.onepriceUpdate(submitData)
    } else {
      // 新增模式
      console.log('➕ 新增一口价')
      result = await api.service.onepriceAdd(submitData)
    }

    if (result.code === '200' || result.code === 200) {
      ElMessage.success(isEdit.value ? '修改成功' : '新增成功')
      dialogVisible.value = false
      getTableDataList()
    } else {
      ElMessage.error(result.message || result.msg || result.meg || '操作失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交失败:', error)
      ElMessage.error('操作失败')
    }
  } finally {
    submitLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    serviceId: null,
    regionPath: [],
    provinceId: null,
    cityId: null,
    areaId: null,
    originalPrice: null, // 新增原价字段
    price: null,
    status: 1,
    priority: 0,
    num: 1,
    effectiveBegin: '',
    effectiveEnd: '',
    configs: [],
    remark: ''
  })

  formRef.value?.resetFields()
}

// 对话框关闭处理
const handleDialogClose = () => {
  resetForm()
}

// 服务项目搜索
const searchServices = async (query) => {
  serviceSearchLoading.value = true
  try {
    console.log('🔍 搜索服务项目，关键词:', query)

    // 构建搜索参数
    const params = {
      pageSize: 50
    }

    // 如果有搜索词，添加到参数中
    if (query && query.trim() !== '') {
      params.title = query.trim()
    }

    const result = await api.service.serviceList(params)

    if (result.code === '200' || result.code === 200) {
      const newServices = result.data.list || []
      console.log('🔍 搜索到的服务:', newServices.length, '条')

      // 如果是搜索（有关键词），直接替换列表
      if (query && query.trim() !== '') {
        // 保留当前编辑中的服务项目，然后添加搜索结果
        const currentEditService = serviceList.value.find(s => s.id === form.serviceId)
        if (currentEditService && !newServices.find(s => s.id === currentEditService.id)) {
          serviceList.value = [currentEditService, ...newServices]
        } else {
          serviceList.value = newServices
        }
      } else {
        // 如果是初始加载（无关键词），直接设置列表
        serviceList.value = newServices
      }

      console.log('🔍 服务列表更新完成，总计:', serviceList.value.length, '条')
    } else {
      console.error('搜索服务失败:', result.message || result.msg)
      ElMessage.error(result.message || result.msg || '搜索服务失败')
    }
  } catch (error) {
    console.error('搜索服务失败:', error)
    ElMessage.error('搜索服务失败')
  } finally {
    serviceSearchLoading.value = false
  }
}

// 服务项目选择器获得焦点时处理
const handleServiceFocus = async () => {
  // 如果服务列表为空，加载默认列表
  if (serviceList.value.length === 0) {
    console.log('🔍 服务选择器获得焦点，加载默认服务列表')
    await loadServiceList()
  }
}

// 配置项值变化处理
const handleConfigValueChange = (config, value) => {
  console.log('🔄 配置项值变化:', config.problemDesc, value, 'inputType:', config.inputType)

  // 确保值始终为数组格式，以便统一处理
  if (config.inputType === 4) {
    // 多选：保持数组格式
    config.values = Array.isArray(value) ? value : (value ? [value] : [])
  } else {
    // 单选：转换为数组格式（包含一个元素）
    config.values = value ? [value] : []
  }

  console.log('📝 配置项最终值:', config.values)
}

// 服务项目变化处理
const handleServiceChange = async (serviceId) => {
  console.log('🔄 服务项目变化:', serviceId)
  if (serviceId) {
    if (isEdit.value) {
      // 编辑模式下，保持已有的配置项值
      await loadServiceConfigs(serviceId, true)
    } else {
      // 新增模式下，不保持已有值，清空后重新加载
      form.configs = []
      await loadServiceConfigs(serviceId, false)
    }
  } else {
    // 清空配置项
    form.configs = []
  }
}

// 加载服务配置项
const loadServiceConfigs = async (serviceId, preserveExistingValues = true) => {
  if (!serviceId) return

  try {
    console.log('🔧 开始加载服务配置项，serviceId:', serviceId, '保留现有值:', preserveExistingValues)
    const result = await api.service.onepriceSettingList({ serviceId })

    if (result.code === '200' || result.code === 200) {
      const configs = result.data || []
      console.log('📋 获取到的配置项原始数据:', configs)

      // 保存现有的配置项值（如果需要）
      const existingValues = {}
      if (preserveExistingValues && isEdit.value) {
        form.configs.forEach(config => {
          if (config.settingId && config.values !== undefined) {
            existingValues[config.settingId] = config.values
          }
        })
        console.log('💾 保存的现有配置项值:', existingValues)
      }

      // 转换配置项数据格式
      form.configs = configs.map(config => {
        const settingId = config.id

        const configItem = {
          settingId: settingId,
          problemDesc: config.problemDesc || `配置项${settingId}`,
          problemContent: config.problemContent || '',
          inputType: config.inputType,
          isRequired: config.isRequired,
          values: existingValues[settingId] !== undefined
            ? (Array.isArray(existingValues[settingId]) ? existingValues[settingId] : [existingValues[settingId]])
            : [], // 统一使用数组格式
          optionsList: config.options ? JSON.parse(config.options) : []
        }

        console.log(`📝 配置项 ${settingId} (${config.problemDesc}):`, configItem)
        return configItem
      })

      console.log('✅ 加载服务配置项成功，共', form.configs.length, '个配置项')
    } else {
      console.error('❌ 获取配置项失败:', result.message || result.msg)
      ElMessage.error(result.message || result.msg || '加载服务配置项失败')
    }
  } catch (error) {
    console.error('❌ 加载服务配置项异常:', error)
    ElMessage.error('加载服务配置项失败')
  }
}

// 获取输入类型文本
const getInputTypeText = (inputType) => {
  const typeMap = {
    1: '输入框',
    2: '上传图片',
    3: '单选',
    4: '多选'
  }
  return typeMap[inputType] || '未知类型'
}

// 删除配置项
const removeConfig = async (index) => {
  const config = form.configs[index]

  // 如果是必填配置项，给出警告
  if (config.isRequired === 1) {
    try {
      await ElMessageBox.confirm(
        `"${config.problemDesc}" 是必填配置项，确定要删除吗？`,
        '删除确认',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    } catch (error) {
      // 用户取消删除
      return
    }
  }

  // 执行删除
  form.configs.splice(index, 1)
  console.log(`🗑️ 已删除配置项: ${config.problemDesc}`)
}

// 地区变化处理（搜索表单）
const handleRegionChange = (value) => {
  console.log('🔄 搜索地区选择变化:', value)

  if (value && value.length > 0) {
    // 根据选择的层级设置对应的ID
    searchForm.provinceId = value[0] || null
    searchForm.cityId = value[1] || null
    searchForm.areaId = value[2] || null
  } else {
    searchForm.provinceId = null
    searchForm.cityId = null
    searchForm.areaId = null
  }
}

// 表单地区变化处理
const handleFormRegionChange = (value) => {
  console.log('🔄 表单地区选择变化:', value)

  if (value && value.length > 0) {
    // 根据选择的层级设置对应的ID
    form.provinceId = value[0] || null
    form.cityId = value[1] || null
    form.areaId = value[2] || null
  } else {
    form.provinceId = null
    form.cityId = null
    form.areaId = null
  }
}

// 获取地区数据
const getRegionData = async () => {
  try {
    console.log('🌍 开始获取省市区数据')
    const result = await api.technician.getCityTree()

    if (result.code === '200' || result.code === 200) {
      regionOptions.value = result.data || []
      console.log('✅ 省市区数据加载成功，共', regionOptions.value.length, '个省份')
    } else {
      console.error('❌ 获取省市区数据失败:', result.message || result.msg)
      ElMessage.error(result.message || result.msg || '获取省市区数据失败')
      regionOptions.value = []
    }
  } catch (error) {
    console.error('❌ 获取省市区数据异常:', error)
    ElMessage.error('获取省市区数据失败，请检查网络连接')
    regionOptions.value = []
  }
}

// 加载服务列表
const loadServiceList = async (searchQuery = '') => {
  try {
    console.log('📋 加载服务列表，搜索词:', searchQuery)
    const params = { pageSize: 1000 }
    if (searchQuery) {
      params.title = searchQuery
    }

    const result = await api.service.serviceList(params)
    if (result.code === '200' || result.code === 200) {
      const newServices = result.data.list || []
      console.log('📋 获取到服务列表:', newServices.length, '条')

      // 直接设置服务列表，不进行合并操作
      serviceList.value = newServices
    } else {
      console.error('❌ 获取服务列表失败:', result.message || result.msg)
      ElMessage.error(result.message || result.msg || '获取服务列表失败')
    }
  } catch (error) {
    console.error('❌ 加载服务列表异常:', error)
    ElMessage.error('加载服务列表失败')
  }
}

// 分页处理
const handleSizeChange = (size) => {
  searchForm.pageSize = size
  handleCurrentChange(1)
}

const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getTableDataList()
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return dateString.split(' ')[0]
}

// 获取地区显示名称
const getRegionDisplayName = () => {
  if (!form.regionPath || form.regionPath.length === 0) {
    return '全国通用'
  }

  // 根据选择的层级构建显示名称
  const names = []
  let currentOptions = regionOptions.value

  for (let i = 0; i < form.regionPath.length; i++) {
    const id = form.regionPath[i]
    const option = currentOptions.find(opt => opt.id === id)
    if (option) {
      names.push(option.trueName)
      currentOptions = option.children || []
    }
  }

  return names.join(' > ') || '未知地区'
}

// 生命周期
onMounted(async () => {
  console.log('🚀 页面开始初始化...')

  // 并行加载数据，提高加载速度
  try {
    await Promise.all([
      getTableDataList(), // 加载一口价列表
      getRegionData() // 加载地区数据
    ])

    // 服务列表按需加载，不在初始化时加载，避免不必要的请求
    console.log('✅ 页面初始化完成')
    console.log('  - 一口价数据:', tableData.value.length, '条')
    console.log('  - 地区数据:', regionOptions.value.length, '个省份')
  } catch (error) {
    console.error('❌ 页面初始化失败:', error)
    ElMessage.error('页面初始化失败，请刷新重试')
  }
})
</script>

<style scoped>
.service-oneprice {
  padding: 0px;
}

.time-column p {
  margin: 0;
  line-height: 1.8;
  font-size: 12px;
}

.time-column p:first-child {
  color: #303133;
}

.time-column p:last-child {
  color: #909399;
}

.table-operate {
  display: flex;
  gap: 8px;
}

/* 统一的容器样式 */
.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 统一的搜索表单样式 */
.search-form-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.search-form .el-form-item {
  margin-bottom: 16px;
  margin-right: 20px;
}

.search-form .el-form-item:last-child {
  margin-right: 0;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.search-form .el-input__inner,
.search-form .el-select .el-input__inner {
  font-size: 14px;
}

/* 统一的表格样式 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 15px 8px !important;
}

.el-table .el-table__body-wrapper td {
  font-size: 14px !important;
  padding: 12px 8px !important;
  color: #333 !important;
}

.el-table .el-table__row:hover {
  background-color: #f8f9fa !important;
}

/* 按钮样式优化 */
.el-button {
  font-size: 14px;
  padding: 8px 16px;
}

.el-button + .el-button {
  margin-left: 10px;
}

/* 配置项样式 */
.configs-container {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 15px;
  background: #fafafa;
}

.config-item {
  margin-bottom: 15px;
  padding: 10px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.config-item:last-child {
  margin-bottom: 10px;
}

/* 配置项信息显示 */
.config-info {
  text-align: left;
}

.config-label {
  margin: 0 0 3px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.config-desc {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

/* 表格中的配置项显示 */
.configs-display {
  max-width: 240px;
}

.config-item-display {
  margin-bottom: 3px;
}

.config-item-display:last-child {
  margin-bottom: 0;
}

/* 无配置项提示 */
.no-configs {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 4px;
}

/* 灰色文字 */
.text-muted {
  color: #909399;
}

/* 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

:deep(.el-input__inner),
:deep(.el-select .el-input__inner),
:deep(.el-textarea__inner) {
  font-size: 14px;
}

/* 表格操作按钮样式 */
.table-operate .el-button {
  margin-right: 8px;
}

.table-operate .el-button:last-child {
  margin-right: 0;
}

/* 地区提示样式 */
.region-tips {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
}

.region-tips p {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: 10px;
  }

  .search-form-container {
    padding: 12px;
  }
}
</style>
