<template>
  <div class="shop-refund">
    <!-- 顶部导航 -->
    <TopNav title="订单退款管理" />

    <div class="content-container">
      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form ref="searchFormRef" :model="searchForm" :inline="true" class="search-form">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="服务项目名" prop="goodsName">
                <el-input size="default" v-model="searchForm.goodsName" placeholder="请输入服务项目名" clearable
                  style="width: 200px" />
              </el-form-item>
              <el-form-item label="订单号" prop="orderCode">
                <el-input size="default" v-model="searchForm.orderCode" placeholder="请输入订单号" clearable
                  style="width: 200px" />
              </el-form-item>
              <el-form-item label="状态" prop="status">
                <el-select size="default" v-model="searchForm.status" placeholder="请选择状态" clearable
                  style="width: 120px">
                  <el-option label="全部" :value="0" />
                  <el-option label="申请中" :value="1" />
                  <el-option label="已退款" :value="2" />
                  <el-option label="已驳回" :value="3" />
                </el-select>
              </el-form-item>
              <el-form-item label="选择城市" prop="cityId">
                <el-cascader size="default" v-model="searchForm.cityId" :options="cityOptions" :props="cascaderProps"
                  placeholder="请选择城市" clearable style="width: 200px" @change="handleCityChange" />
              </el-form-item>
              <el-form-item>
                <LbButton size="default" type="primary" @click="handleSearch">
                  搜索
                </LbButton>
                <LbButton size="default" @click="handleReset">
                  重置
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 表格 -->
      <div class="table-container">
        <el-table v-loading="loading" :data="tableData"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontSize: '16px', fontWeight: '600' }"
          :cell-style="{ fontSize: '14px', padding: '12px 8px' }" :expand-row-keys="expandedRows" row-key="id"
          style="width: 100%">
          <el-table-column type="expand" width="55" v-if="hasAnyDiffRefunds">
            <template #default="{ row }">
              <div class="diff-refund-container" v-if="row.diffPriceRefunds && row.diffPriceRefunds.length > 0">
                <h4 class="diff-refund-title">差价退款子订单</h4>
                <el-table :data="row.diffPriceRefunds"
                  :header-cell-style="{ background: '#f0f2f5', color: '#606266', fontSize: '14px', fontWeight: '500' }"
                  :cell-style="{ fontSize: '13px', padding: '8px' }" style="width: 100%">
                  <el-table-column prop="id" label="差价退款ID" width="120" align="center" />
                  <el-table-column prop="refundNo" label="退款单号" min-width="200" align="center" />
                  <el-table-column label="退款金额" width="120" align="center">
                    <template #default="scope">
                      <span class="amount-text">¥{{ scope.row.applyAmount }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="status" label="状态" width="100" align="center">
                    <template #default="scope">
                      <el-tag :type="getDiffRefundStatusType(scope.row.status)">
                        {{ getDiffRefundStatusText(scope.row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="申请时间" width="160" align="center">
                    <template #default="scope">
                      <div class="time-column">
                        <p>{{ formatDate(scope.row.createTime) }}</p>
                        <p>{{ formatTime(scope.row.createTime) }}</p>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="200" align="center">
                    <template #default="scope">
                      <LbButton v-if="scope.row.status == 1" size="small" type="success"
                        @click="handleDiffApprove(scope.row)">
                        同意退款
                      </LbButton>
                      <LbButton v-if="scope.row.status == 1" size="small" type="danger"
                        @click="handleDiffReject(scope.row)">
                        拒绝退款
                      </LbButton>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="id" label="ID" width="80" align="center" />

          <el-table-column prop="goodsCover" label="服务封面" width="120" align="center">
            <template #default="scope">
              <LbImage :src="scope.row.goodsCover" width="80" height="50" />
            </template>
          </el-table-column>

          <el-table-column prop="goodsName" label="服务项目" min-width="150" align="center" />

          <el-table-column prop="orderCode" label="订单号" min-width="200" align="center">
            <template #default="scope">
              <el-link type="primary" @click="handleViewOrder(scope.row)">
                {{ scope.row.orderCode }}
              </el-link>
            </template>
          </el-table-column>

          <el-table-column prop="userName" label="用户" width="100" align="center" />

          <el-table-column prop="coachName" label="师傅" width="100" align="center" />

          <el-table-column label="金额信息" width="200" align="center">
            <template #default="scope">
              <div class="amount-info">
                <p>申请: ¥{{ scope.row.applyPrice }}</p>
                <p>退款: ¥{{ scope.row.refundPrice }}</p>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="退款状态" width="120" align="center">
            <template #default="scope">
              <div>
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
                <!-- 调试信息 -->
                <!-- <div style="font-size: 18px; color: #999; margin-top: 2px;">
                  {{ scope.row.status }} ({{ typeof scope.row.status }})
                </div> -->
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="payType" label="订单状态" width="150" align="center">
            <template #default="scope">
              <el-tag :type="getOrderStatusType(scope.row.payType)">
                {{ getOrderStatusText(scope.row.payType) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="emptyRunStatus" label="师傅空跑费" width="120" align="center">
            <template #default="scope">
              <el-tag :type="getEmptyRunStatusType(scope.row.emptyRunStatus)">
                {{ getEmptyRunStatusText(scope.row.emptyRunStatus) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="申请时间" width="160" align="center">
            <template #default="scope">
              <div class="time-column">
                <p>{{ formatDate(scope.row.createTime) }}</p>
                <p>{{ formatTime(scope.row.createTime) }}</p>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="400" fixed="right" align="center">
            <template #default="scope">
              <!-- 调试信息 -->
              <!-- <div style="font-size: 18px; color: #999; margin-bottom: 5px;">
                状态: {{ scope.row.status }}, 类型: {{ typeof scope.row.status }},
                == 1: {{ scope.row.status == 1 }}, === 1: {{ scope.row.status === 1 }}
              </div> -->

              <LbButton v-if="scope.row.status == 1" size="default" type="success" @click="handleApprove(scope.row)">
                同意退款
              </LbButton>
              <LbButton v-if="scope.row.status == 1" size="default" type="danger" @click="handleReject(scope.row)">
                拒绝退款
              </LbButton>
              <LbButton size="default" type="primary" @click="handleViewDetail(scope.row)">
                查看详情
              </LbButton>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage :page="searchForm.pageNum" :page-size="searchForm.pageSize" :total="total"
        @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />
    </div>

    <!-- 同意退款对话框 -->
    <el-dialog v-model="approveVisible" title="同意退款" width="500px" :before-close="handleApproveClose">
      <el-form ref="approveFormRef" :model="approveForm" :rules="approveRules" label-width="100px">
        <el-form-item label="退款金额" prop="price">
          <el-input-number v-model="approveForm.price" :precision="2" :min="0" :max="currentRefund?.applyPrice || 0"
            placeholder="请输入退款金额" style="width: 100%" />
          <div class="form-tip">
            申请金额: ¥{{ currentRefund?.applyPrice || 0 }}
          </div>
        </el-form-item>
        <el-form-item label="备注" prop="text">
          <el-input v-model="approveForm.text" type="textarea" :rows="3" placeholder="请输入备注信息（可选）" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="handleApproveClose">取消</LbButton>
          <LbButton type="primary" :loading="approveLoading" @click="handleApproveSubmit">
            确定同意
          </LbButton>
        </span>
      </template>
    </el-dialog>

    <!-- 拒绝退款对话框 -->
    <el-dialog v-model="rejectVisible" title="拒绝退款" width="500px" :before-close="handleRejectClose">
      <el-form ref="rejectFormRef" :model="rejectForm" :rules="rejectRules" label-width="100px">
        <el-form-item label="退款金额" prop="price">
          <el-input-number v-model="rejectForm.price" :precision="2" :min="0" :max="currentRefund?.applyPrice || 0"
            placeholder="请输入退款金额" style="width: 100%" />
          <div class="form-tip">
            申请金额: ¥{{ currentRefund?.applyPrice || 0 }}
          </div>
        </el-form-item>
        <el-form-item label="拒绝原因" prop="text">
          <el-input v-model="rejectForm.text" type="textarea" :rows="4" placeholder="请输入拒绝原因（必填）" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="handleRejectClose">取消</LbButton>
          <LbButton type="danger" :loading="rejectLoading" @click="handleRejectSubmit">
            确定拒绝
          </LbButton>
        </span>
      </template>
    </el-dialog>

    <!-- 详情查看对话框 -->
    <el-dialog title="退款详情" v-model="detailVisible" width="700px">
      <div v-if="currentRefund" class="detail-content">
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="detail-item">
            <label>退款ID:</label>
            <span>{{ currentRefund.id }}</span>
          </div>
          <div class="detail-item">
            <label>订单号:</label>
            <span>{{ currentRefund.orderCode }}</span>
          </div>
          <div class="detail-item">
            <label>退款订单号:</label>
            <span>{{ currentRefund.refOrderCode || currentRefund.refundOrderCode }}</span>
          </div>
          <div class="detail-item">
            <label>外部退款号:</label>
            <span>{{ currentRefund.outRefundNo || '无' }}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>服务信息</h4>
          <div class="detail-item">
            <label>服务项目:</label>
            <span>{{ currentRefund.goodsName }}</span>
          </div>
          <div class="detail-item">
            <label>服务封面:</label>
            <div class="detail-image">
              <LbImage :src="currentRefund.goodsCover" width="100" height="60" />
            </div>
          </div>
          <div class="detail-item">
            <label>服务数量:</label>
            <span>{{ currentRefund.num }}</span>
          </div>
          <div class="detail-item">
            <label>总数量:</label>
            <span>{{ currentRefund.countNum }}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>用户信息</h4>
          <div class="detail-item">
            <label>用户ID:</label>
            <span>{{ currentRefund.userId }}</span>
          </div>
          <div class="detail-item">
            <label>用户姓名:</label>
            <span>{{ currentRefund.userName }}</span>
          </div>
          <div class="detail-item">
            <label>手机号:</label>
            <span>{{ currentRefund.mobile }}</span>
          </div>
          <div class="detail-item">
            <label>地址:</label>
            <span>{{ currentRefund.address }}</span>
          </div>
          <div class="detail-item">
            <label>详细地址:</label>
            <span>{{ currentRefund.addressInfo }}</span>
          </div>
          <div class="detail-item">
            <label>师傅姓名:</label>
            <span>{{ currentRefund.coachName }}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>金额信息</h4>
          <div class="detail-item">
            <label>退款金额:</label>
            <span class="amount">¥{{ currentRefund.refundPrice }}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>时间信息</h4>
          <div class="detail-item">
            <label>申请时间:</label>
            <span>{{ currentRefund.createTime }}</span>
          </div>
          <div class="detail-item">
            <label>退款时间:</label>
            <span>{{ currentRefund.refundTime || '未退款' }}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>状态信息</h4>
          <div class="detail-item">
            <label>退款状态:</label>
            <el-tag :type="getStatusType(currentRefund.status)">
              {{ getStatusText(currentRefund.status) }}
            </el-tag>
          </div>
          <div class="detail-item">
            <label>订单状态:</label>
            <el-tag :type="getOrderStatusType(currentRefund.payType)">
              {{ getOrderStatusText(currentRefund.payType) }}
            </el-tag>
          </div>
          <div class="detail-item">
            <label>师傅空跑费:</label>
            <el-tag :type="getEmptyRunStatusType(currentRefund.emptyRunStatus)">
              {{ getEmptyRunStatusText(currentRefund.emptyRunStatus) }}
            </el-tag>
          </div>
          <div class="detail-item">
            <label>退款说明:</label>
            <span>{{ currentRefund.refundText || '无' }}</span>
          </div>
        </div>

        <!-- 师傅头像信息 -->
        <div class="detail-section" v-if="currentRefund.selfImg">
          <h4>师傅信息</h4>
          <div class="detail-item">
            <label>师傅头像:</label>
            <div class="detail-image">
              <el-image :src="'data:image/jpeg;base64,' + currentRefund.selfImg"
                style="width: 80px; height: 80px; border-radius: 50%;" fit="cover"
                :preview-src-list="['data:image/jpeg;base64,' + currentRefund.selfImg]" />
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 差价同意退款对话框 -->
    <el-dialog v-model="diffApproveVisible" title="差价同意退款" width="500px" :before-close="handleDiffApproveClose">
      <el-form ref="diffApproveFormRef" :model="diffApproveForm" :rules="diffApproveRules" label-width="100px">
        <el-form-item label="退款金额" prop="price">
          <el-input-number v-model="diffApproveForm.price" :precision="2" :min="0"
            :max="currentDiffRefund?.applyAmount || 0" placeholder="请输入退款金额" style="width: 100%" />
          <div class="form-tip">
            申请金额: ¥{{ currentDiffRefund?.applyAmount || 0 }}
          </div>
        </el-form-item>
        <el-form-item label="备注" prop="text">
          <el-input v-model="diffApproveForm.text" type="textarea" :rows="3" placeholder="请输入备注信息（可选）" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="handleDiffApproveClose">取消</LbButton>
          <LbButton type="primary" :loading="diffApproveLoading" @click="handleDiffApproveSubmit">
            确定同意
          </LbButton>
        </span>
      </template>
    </el-dialog>

    <!-- 差价拒绝退款对话框 -->
    <el-dialog v-model="diffRejectVisible" title="差价拒绝退款" width="500px" :before-close="handleDiffRejectClose">
      <el-form ref="diffRejectFormRef" :model="diffRejectForm" :rules="diffRejectRules" label-width="100px">
        <el-form-item label="退款金额" prop="price">
          <el-input-number v-model="diffRejectForm.price" :precision="2" :min="0"
            :max="currentDiffRefund?.applyAmount || 0" placeholder="请输入退款金额" style="width: 100%" />
          <div class="form-tip">
            申请金额: ¥{{ currentDiffRefund?.applyAmount || 0 }}
          </div>
        </el-form-item>
        <el-form-item label="拒绝原因" prop="text">
          <el-input v-model="diffRejectForm.text" type="textarea" :rows="4" placeholder="请输入拒绝原因（必填）" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="handleDiffRejectClose">取消</LbButton>
          <LbButton type="danger" :loading="diffRejectLoading" @click="handleDiffRejectSubmit">
            确定拒绝
          </LbButton>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbImage from '@/components/common/LbImage.vue'
import LbPage from '@/components/common/LbPage.vue'

// 导入API
import { api } from '@/api-v2'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const searchFormRef = ref(null)
const approveFormRef = ref(null)
const rejectFormRef = ref(null)
const diffApproveFormRef = ref(null)
const diffRejectFormRef = ref(null)

// 对话框状态
const approveVisible = ref(false)
const rejectVisible = ref(false)
const detailVisible = ref(false)
const diffApproveVisible = ref(false)
const diffRejectVisible = ref(false)
const approveLoading = ref(false)
const rejectLoading = ref(false)
const diffApproveLoading = ref(false)
const diffRejectLoading = ref(false)
const currentRefund = ref(null)
const currentDiffRefund = ref(null)

// 展开行状态
const expandedRows = ref([])

// 计算属性：判断是否有任何订单包含差价退款
const hasAnyDiffRefunds = computed(() => {
  return tableData.value.some(row =>
    row.diffPriceRefunds && row.diffPriceRefunds.length > 0
  )
})

// 城市选择相关
const cityOptions = ref([])
const cascaderProps = {
  value: 'id',
  label: 'trueName',
  children: 'children',
  multiple: false,
  emitPath: true,
  checkStrictly: false,
  expandTrigger: 'hover'
}

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10,
  goodsName: '',
  orderCode: '',
  status: '',
  cityId: []
})

// 同意退款表单
const approveForm = reactive({
  id: null,
  price: 0,
  text: ''
})

// 拒绝退款表单
const rejectForm = reactive({
  id: null,
  price: 0,
  text: ''
})

// 差价同意退款表单
const diffApproveForm = reactive({
  id: null,
  price: 0,
  text: ''
})

// 差价拒绝退款表单
const diffRejectForm = reactive({
  id: null,
  price: 0,
  text: ''
})

// 表单验证规则
const approveRules = {
  price: [
    { required: true, message: '请输入退款金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '退款金额不能小于0', trigger: 'blur' }
  ]
}

const rejectRules = {
  price: [
    { required: true, message: '请输入退款金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '退款金额不能小于0', trigger: 'blur' }
  ],
  text: [
    { required: true, message: '请输入拒绝原因', trigger: 'blur' }
  ]
}

// 差价退款表单验证规则
const diffApproveRules = {
  price: [
    { required: true, message: '请输入退款金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '退款金额不能小于0', trigger: 'blur' }
  ]
}

const diffRejectRules = {
  price: [
    { required: true, message: '请输入退款金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '退款金额不能小于0', trigger: 'blur' }
  ],
  text: [
    { required: true, message: '请输入拒绝原因', trigger: 'blur' }
  ]
}

// 获取列表数据
const getTableDataList = async (flag) => {
  if (flag) searchForm.pageNum = 1

  loading.value = true
  try {
    // 构建查询参数
    const params = {
      pageNum: searchForm.pageNum,
      pageSize: searchForm.pageSize
    }

    // 添加可选参数
    if (searchForm.goodsName) params.goodsName = searchForm.goodsName
    if (searchForm.orderCode) params.orderCode = searchForm.orderCode
    if (searchForm.status !== null && searchForm.status !== '') params.status = searchForm.status
    if (searchForm.cityId && searchForm.cityId.length > 0) {
      params.cityId = searchForm.cityId.join(',')
    }

    // 使用API-V2调用方式
    const result = await api.shop.orderRefundList(params)
    console.log('📋 订单退款列表数据 (API-V2):', result)

    // 处理API响应格式
    if (result.code === '200') {
      const data = result.data
      // 处理数据，确保所有字段都有默认值
      const processedList = (data.list || []).map(item => ({
        ...item,
        goodsName: item.goodsName || '未知服务',
        goodsCover: item.goodsCover || '',
        userName: item.userName || '未知用户',
        coachName: item.coachName || '未知师傅',
        orderCode: item.orderCode || '',
        refundOrderCode: item.refundOrderCode || '',
        outRefundNo: item.outRefundNo || '',
        applyPrice: item.applyPrice || 0,
        refundPrice: item.refundPrice || 0,
        status: item.status || 0,
        payType: item.payType || 0,  // 订单状态
        emptyRunStatus: item.emptyRunStatus || 0,  // 师傅申请空跑费状态
        createTime: item.createTime || '',
        diffPriceRefunds: item.diffPriceRefunds || []
      }))

      tableData.value = processedList
      total.value = data.totalCount || data.total || 0

      // 自动展开有差价退款的行
      expandedRows.value = tableData.value
        .filter(row => row.diffPriceRefunds && row.diffPriceRefunds.length > 0)
        .map(row => row.id)

      // 调试：检查状态数据类型
      if (tableData.value.length > 0) {
        console.log('🔍 第一条数据的状态:', {
          status: tableData.value[0].status,
          statusType: typeof tableData.value[0].status,
          statusValue: tableData.value[0].status,
          isEqual1: tableData.value[0].status === 1,
          isEqual1Loose: tableData.value[0].status == 1
        })
      }

      console.log('📊 处理后的数据:', {
        list: tableData.value,
        total: total.value,
        pageNum: data.pageNum,
        pageSize: data.pageSize,
        expandedRows: expandedRows.value
      })
    } else {
      console.error('❌ API响应错误:', result)
      ElMessage.error(result.meg || result.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取订单退款列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  getTableDataList(1)
}

// 城市变更处理
const handleCityChange = (value) => {
  console.log('🏙️ 城市选择变更:', value)
  if (value && value.length > 0) {
    const selectedCityId = value[value.length - 1]
    console.log('🏙️ 选中的城市ID:', selectedCityId)
  } else {
    console.log('🏙️ 清空城市选择')
  }
}

// 获取城市数据
const getCityData = async () => {
  try {
    console.log('🌳 开始获取城市树数据')
    const result = await api.account.agentCityTree()

    if (result.code === '200' || result.code === 200) {
      cityOptions.value = result.data || []
      console.log('✅ 城市树数据获取成功:', cityOptions.value)
    } else {
      console.error('❌ 获取城市数据失败:', result.msg)
      ElMessage.error(result.msg || '获取城市数据失败')
    }
  } catch (error) {
    console.error('❌ 获取城市数据异常:', error)
    ElMessage.error('获取城市数据失败')
  }
}

// 重置处理
const handleReset = () => {
  searchForm.goodsName = ''
  searchForm.orderCode = ''
  searchForm.status = ''
  searchForm.cityId = []
  searchFormRef.value?.resetFields()
  getTableDataList(1)
}

// 分页处理
const handleSizeChange = (size) => {
  searchForm.pageSize = size
  handleCurrentChange(1)
}

const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getTableDataList()
}

// 获取状态类型
const getStatusType = (status) => {
  // 转换为数字进行比较，兼容字符串和数字类型
  const statusNum = Number(status)
  switch (statusNum) {
    case 1: return 'warning'  // 申请中
    case 2: return 'success'  // 已退款
    case 3: return 'danger'   // 已驳回
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  // 转换为数字进行比较，兼容字符串和数字类型
  const statusNum = Number(status)
  switch (statusNum) {
    case 1: return '申请中'
    case 2: return '已退款'
    case 3: return '已驳回'
    default: return '未知'
  }
}

// 获取差价退款状态类型
const getDiffRefundStatusType = (status) => {
  const statusNum = Number(status)
  switch (statusNum) {
    case 1: return 'warning'  // 申请中
    case 2: return 'success'  // 已退款
    case 3: return 'danger'   // 已驳回
    default: return 'info'
  }
}

// 获取差价退款状态文本
const getDiffRefundStatusText = (status) => {
  const statusNum = Number(status)
  switch (statusNum) {
    case 1: return '申请中'
    case 2: return '已退款'
    case 3: return '已驳回'
    default: return '未知'
  }
}

// 获取订单状态类型
const getOrderStatusType = (payType) => {
  const typeNum = Number(payType)
  switch (typeNum) {
    case -3:
    case -2:
    case 1: return 'warning'  // 待报价、已报价、待支付
    case 2:
    case 3:
    case 4:
    case 5:
    case 6: return 'primary'  // 进行中的状态
    case 7: return 'success'  // 已完成
    case -1: return 'danger'  // 取消订单
    default: return 'info'
  }
}

// 获取订单状态文本
const getOrderStatusText = (payType) => {
  const typeNum = Number(payType)
  switch (typeNum) {
    case -3: return '待报价'
    case -2: return '已报价(未选择报价)'
    case -1: return '取消订单'
    case 1: return '待支付'
    case 2: return '已支付，师傅未接单'
    case 3: return '师傅已接单待上门'
    case 4: return '师傅已出发待到达'
    case 5: return '待服务（已上门）'
    case 6: return '服务中（开始服务）'
    case 7: return '已完成'
    default: return '未知状态'
  }
}

// 获取师傅空跑费状态类型
const getEmptyRunStatusType = (status) => {
  const statusNum = Number(status)
  switch (statusNum) {
    case 0: return 'info'     // 未申请
    case 1: return 'warning' // 已申请
    default: return 'info'
  }
}

// 获取师傅空跑费状态文本
const getEmptyRunStatusText = (status) => {
  const statusNum = Number(status)
  switch (statusNum) {
    case 0: return '未申请'
    case 1: return '已申请'
    default: return '未知'
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return dateString.split(' ')[0]
}

const formatTime = (dateString) => {
  if (!dateString) return '-'
  return dateString.split(' ')[1]
}

// 查看订单详情
const handleViewOrder = (row) => {
  // 这里可以跳转到订单详情页面或打开订单详情对话框
  console.log('查看订单:', row.orderCode)
  ElMessage.info('订单详情功能待开发')
}

// 查看退款详情
const handleViewDetail = async (row) => {
  try {
    console.log('🔍 查看退款详情，ID:', row.id)

    const result = await api.shop.orderRefundDetail({ id: row.id })
    console.log('📋 退款详情API响应:', result)

    if (result.code === '200') {
      // 根据API返回的数据结构，需要访问 result.data.data
      const detailData = result.data.data || result.data
      console.log('📊 退款详情数据:', detailData)

      currentRefund.value = detailData
      detailVisible.value = true
    } else {
      ElMessage.error(result.msg || result.msg || '获取退款详情失败')
    }
  } catch (error) {
    console.error('获取退款详情失败:', error)
    ElMessage.error('获取退款详情失败')
  }
}

// 同意退款处理
const handleApprove = (row) => {
  currentRefund.value = row
  approveForm.id = row.id
  approveForm.price = row.applyPrice
  approveForm.text = ''
  approveVisible.value = true
}

// 拒绝退款处理
const handleReject = (row) => {
  currentRefund.value = row
  rejectForm.id = row.id
  rejectForm.price = row.applyPrice
  rejectForm.text = ''
  rejectVisible.value = true
}

// 同意退款对话框关闭
const handleApproveClose = () => {
  approveVisible.value = false
  resetApproveForm()
}

// 拒绝退款对话框关闭
const handleRejectClose = () => {
  rejectVisible.value = false
  resetRejectForm()
}

// 重置同意退款表单
const resetApproveForm = () => {
  approveForm.id = null
  approveForm.price = 0
  approveForm.text = ''
  if (approveFormRef.value) {
    approveFormRef.value.clearValidate()
  }
}

// 重置拒绝退款表单
const resetRejectForm = () => {
  rejectForm.id = null
  rejectForm.price = 0
  rejectForm.text = ''
  if (rejectFormRef.value) {
    rejectFormRef.value.clearValidate()
  }
}

// 提交同意退款
const handleApproveSubmit = async () => {
  try {
    await approveFormRef.value.validate()

    approveLoading.value = true

    const result = await api.shop.passRefund({
      id: approveForm.id,
      price: approveForm.price,
      text: approveForm.text
    })

    if (result.code === '200') {
      ElMessage.success('同意退款成功')
      approveVisible.value = false
      getTableDataList()
    } else {
      ElMessage.error(result.msg || '同意退款失败')
    }
  } catch (error) {
    console.error('同意退款失败:', error)
    ElMessage.error('操作失败')
  } finally {
    approveLoading.value = false
  }
}

// 提交拒绝退款
const handleRejectSubmit = async () => {
  try {
    await rejectFormRef.value.validate()

    rejectLoading.value = true

    const result = await api.shop.noPassRefund({
      id: rejectForm.id,
      price: rejectForm.price,
      text: rejectForm.text
    })

    if (result.code === '200') {
      ElMessage.success('拒绝退款成功')
      rejectVisible.value = false
      getTableDataList()
    } else {
      ElMessage.error(result.meg || '拒绝退款失败')
    }
  } catch (error) {
    console.error('拒绝退款失败:', error)
    ElMessage.error('操作失败')
  } finally {
    rejectLoading.value = false
  }
}

// ==================== 差价退款相关方法 ====================

// 差价同意退款处理
const handleDiffApprove = (row) => {
  currentDiffRefund.value = row
  diffApproveForm.id = row.id
  diffApproveForm.price = row.applyAmount
  diffApproveForm.text = ''
  diffApproveVisible.value = true
}

// 差价拒绝退款处理
const handleDiffReject = (row) => {
  currentDiffRefund.value = row
  diffRejectForm.id = row.id
  diffRejectForm.price = row.applyAmount
  diffRejectForm.text = ''
  diffRejectVisible.value = true
}

// 差价同意退款对话框关闭
const handleDiffApproveClose = () => {
  diffApproveVisible.value = false
  resetDiffApproveForm()
}

// 差价拒绝退款对话框关闭
const handleDiffRejectClose = () => {
  diffRejectVisible.value = false
  resetDiffRejectForm()
}

// 重置差价同意退款表单
const resetDiffApproveForm = () => {
  diffApproveForm.id = null
  diffApproveForm.price = 0
  diffApproveForm.text = ''
  if (diffApproveFormRef.value) {
    diffApproveFormRef.value.clearValidate()
  }
}

// 重置差价拒绝退款表单
const resetDiffRejectForm = () => {
  diffRejectForm.id = null
  diffRejectForm.price = 0
  diffRejectForm.text = ''
  if (diffRejectFormRef.value) {
    diffRejectFormRef.value.clearValidate()
  }
}

// 提交差价同意退款
const handleDiffApproveSubmit = async () => {
  try {
    await diffApproveFormRef.value.validate()

    diffApproveLoading.value = true

    const result = await api.shop.diffPassRefund({
      id: diffApproveForm.id,
      price: diffApproveForm.price,
      text: diffApproveForm.text
    })

    if (result.code === '200') {
      ElMessage.success('差价同意退款成功')
      diffApproveVisible.value = false
      getTableDataList()
    } else {
      ElMessage.error(result.meg || '差价同意退款失败')
    }
  } catch (error) {
    console.error('差价同意退款失败:', error)
    ElMessage.error('操作失败')
  } finally {
    diffApproveLoading.value = false
  }
}

// 提交差价拒绝退款
const handleDiffRejectSubmit = async () => {
  try {
    await diffRejectFormRef.value.validate()

    diffRejectLoading.value = true

    const result = await api.shop.diffNoPassRefund({
      id: diffRejectForm.id,
      price: diffRejectForm.price,
      text: diffRejectForm.text
    })

    if (result.code === '200') {
      ElMessage.success('差价拒绝退款成功')
      diffRejectVisible.value = false
      getTableDataList()
    } else {
      ElMessage.error(result.meg || '差价拒绝退款失败')
    }
  } catch (error) {
    console.error('差价拒绝退款失败:', error)
    ElMessage.error('操作失败')
  } finally {
    diffRejectLoading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  getCityData()
  getTableDataList()
})
</script>

<style scoped>
.shop-refund {
  padding: 0px;
}

.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

:deep(.el-table) {
  border-left: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
}

.time-column p {
  margin: 0;
  line-height: 1.4;
  font-size: 18px;
}

.time-column p:first-child {
  color: #303133;
}

.time-column p:last-child {
  color: #909399;
}

.amount-info p {
  margin: 0;
  line-height: 1.4;
  font-size: 18px;
}

.amount-info p:first-child {
  color: #E6A23C;
}

.amount-info p:last-child {
  color: #67C23A;
  font-weight: 600;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.form-tip {
  font-size: 18px;
  color: #909399;
  margin-top: 5px;
}

/* 详情对话框样式 */
.detail-content {
  padding: 10px 0;
}

.detail-section {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-section:last-child {
  border-bottom: none;
}

.detail-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
  padding: 5px 0;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item label {
  font-weight: 600;
  color: #333;
  width: 100px;
  flex-shrink: 0;
  margin-right: 15px;
}

.detail-item span {
  color: #666;
  flex: 1;
}

.detail-item .amount {
  color: #E6A23C;
  font-weight: 600;
  font-size: 16px;
}

.detail-image {
  margin-top: 5px;
}

/* 统一的容器样式 */
.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 统一的搜索表单样式 */
.search-form {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.search-form .el-input__inner,
.search-form .el-select .el-input__inner {
  font-size: 14px;
}

/* 统一的表格样式 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 15px 8px !important;
}

.el-table .el-table__body-wrapper td {
  font-size: 14px !important;
  padding: 12px 8px !important;
  color: #333 !important;
}

.el-table .el-table__row:hover {
  background-color: #f8f9fa !important;
}

/* 按钮样式优化 */
.el-button {
  font-size: 14px;
  padding: 8px 16px;
}

.el-button+.el-button {
  margin-left: 10px;
}

/* 差价退款相关样式 */
.diff-refund-container {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 10px 0;
}

.diff-refund-title {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}



.amount-text {
  color: #E6A23C;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: 10px;
  }

  .search-form {
    padding: 12px;
  }

  .diff-refund-container {
    padding: 15px;
  }

  .diff-refund-title {
    font-size: 14px;
  }
}
</style>