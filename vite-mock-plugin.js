/**
 * Vite Mock插件
 * 直接在Vite开发服务器中处理Mock API
 */

import Mock from 'mockjs'

// 生成Mock数据
const generateServiceList = (count = 50) => {
  return Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    title: Mock.Random.ctitle(5, 15),
    category: Mock.Random.pick(['家政服务', '维修服务', '清洁服务', '搬家服务', '装修服务']),
    price: Mock.Random.float(50, 500, 2, 2),
    originalPrice: Mock.Random.float(60, 600, 2, 2),
    description: Mock.Random.cparagraph(1, 3),
    image: Mock.Random.image('300x200', Mock.Random.color(), '#FFF', 'png', Mock.Random.word()),
    status: Mock.Random.pick([0, 1]),
    sort: Mock.Random.integer(1, 100),
    viewCount: Mock.Random.integer(100, 10000),
    orderCount: Mock.Random.integer(10, 1000),
    rating: Mock.Random.float(3.5, 5.0, 1, 1),
    tags: Mock.Random.shuffle(['热门', '推荐', '新品', '特价']).slice(0, Mock.Random.integer(1, 3)),
    create_time: Mock.Random.datetime(),
    update_time: Mock.Random.datetime(),
    createTime: Mock.Random.datetime(),
    updateTime: Mock.Random.datetime()
  }))
}

const generateBannerList = (count = 15) => {
  return Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    title: Mock.Random.ctitle(5, 10),
    image: Mock.Random.image('800x400', Mock.Random.color(), '#FFF', 'png', 'Banner'),
    link: Mock.Random.url(),
    sort: Mock.Random.integer(1, 100),
    status: Mock.Random.pick([0, 1]),
    clickCount: Mock.Random.integer(100, 5000),
    startTime: Mock.Random.datetime(),
    endTime: Mock.Random.datetime(),
    create_time: Mock.Random.datetime(),
    update_time: Mock.Random.datetime()
  }))
}

const generateCategoryList = (count = 25) => {
  return Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    name: Mock.Random.ctitle(2, 6),
    parent_id: index < 5 ? 0 : Mock.Random.integer(1, 5),
    icon: Mock.Random.pick(['Service', 'Tool', 'Home', 'Car', 'Beauty']),
    sort: Mock.Random.integer(1, 100),
    status: Mock.Random.pick([0, 1]),
    is_recommend: Mock.Random.pick([0, 1]),
    description: Mock.Random.csentence(5, 20),
    create_time: Mock.Random.datetime(),
    update_time: Mock.Random.datetime()
  }))
}

// 初始化数据
const serviceList = generateServiceList()
const bannerList = generateBannerList()
const categoryList = generateCategoryList()

// 生成分类树
const categoryTreeList = categoryList
  .filter(item => item.parent_id === 0)
  .map(parent => ({
    ...parent,
    children: categoryList.filter(child => child.parent_id === parent.id)
  }))

// 响应格式
const successResponse = (data = null, message = '操作成功') => ({
  code: 200,
  message,
  data,
  timestamp: Date.now()
})

// 创建通用配置获取API的辅助函数
function createConfigAPI(name) {
  return (req, res) => {
    console.log(`⚙️ ${name}配置获取API被调用`)
    const config = Mock.mock({
      'setting1': '@word(5, 10)',
      'setting2': '@integer(1, 100)',
      'setting3|0-1': 1
    })
    const result = successResponse(config)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  }
}

// 创建通用配置保存API的辅助函数
function createSaveAPI(name) {
  return (req, res) => {
    console.log(`💾 ${name}配置保存API被调用`)
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const configData = JSON.parse(body)
      const result = successResponse(configData, `${name}配置保存成功`)
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  }
}

// 创建通用测试API的辅助函数
function createTestAPI(name) {
  return (req, res) => {
    console.log(`🔍 ${name}测试API被调用`)
    const result = successResponse(null, `${name}测试成功`)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  }
}

const pageResponse = (list = [], total = 0, page = 1, pageSize = 10) => ({
  code: 200,
  message: '操作成功',
  data: {
    list,
    total,
    page: parseInt(page),
    pageSize: parseInt(pageSize),
    totalPages: Math.ceil(total / pageSize)
  },
  timestamp: Date.now()
})

const errorResponse = (message = '操作失败', code = 500) => ({
  code,
  message,
  data: null,
  timestamp: Date.now()
})

// 处理分页
const handlePagination = (data, url) => {
  const urlObj = new URL(url, 'http://localhost')
  const page = parseInt(urlObj.searchParams.get('page') || '1')
  const pageSize = parseInt(urlObj.searchParams.get('pageSize') || '10')
  const keyword = urlObj.searchParams.get('keyword') || ''
  
  let filteredData = [...data]
  if (keyword) {
    filteredData = data.filter(item => 
      JSON.stringify(item).toLowerCase().includes(keyword.toLowerCase())
    )
  }
  
  const start = (page - 1) * pageSize
  const end = start + pageSize
  const list = filteredData.slice(start, end)
  
  return pageResponse(list, filteredData.length, page, pageSize)
}

// Mock API路由处理（注意：URL已经去掉了/api前缀）
const mockRoutes = {
  // 服务项目管理
  'GET /service/list': (req, res) => {
    console.log('📋 服务项目列表API被调用:', req.url)
    const fullUrl = 'http://localhost:3000/api' + req.url
    const result = handlePagination(serviceList, fullUrl)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'GET /service/detail': (req, res) => {
    console.log('📄 服务项目详情API被调用:', req.url)
    // 从URL中提取ID
    const urlParts = req.url.split('/')
    const id = parseInt(urlParts[urlParts.length - 1])

    const item = serviceList.find(item => item.id === id)
    if (item) {
      const result = successResponse(item, '获取详情成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    } else {
      const result = errorResponse('项目不存在', 404)
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    }
  },

  'POST /service/add': (req, res) => {
    console.log('➕ 服务项目新增API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const newItem = JSON.parse(body)
      newItem.id = serviceList.length + 1
      newItem.create_time = Mock.Random.datetime()
      newItem.update_time = Mock.Random.datetime()
      serviceList.push(newItem)

      const result = successResponse(newItem, '新增成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'PUT /service/update': (req, res) => {
    console.log('✏️ 服务项目更新API被调用:', req.url)
    // 从URL中提取ID
    const urlParts = req.url.split('/')
    const id = parseInt(urlParts[urlParts.length - 1])

    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const updateData = JSON.parse(body)
      const index = serviceList.findIndex(item => item.id === id)

      if (index !== -1) {
        serviceList[index] = { ...serviceList[index], ...updateData, update_time: Mock.Random.datetime() }
        const result = successResponse(serviceList[index], '更新成功')
        res.setHeader('Content-Type', 'application/json')
        res.end(JSON.stringify(result))
      } else {
        const result = errorResponse('项目不存在', 404)
        res.setHeader('Content-Type', 'application/json')
        res.end(JSON.stringify(result))
      }
    })
  },

  'GET /service/statistics': (req, res) => {
    console.log('📊 统计API被调用')
    const result = successResponse({
      totalServices: serviceList.length,
      activeServices: serviceList.filter(item => item.status === 1).length,
      totalCategories: categoryList.length,
      totalBanners: bannerList.length,
      monthlyOrders: Mock.Random.integer(100, 1000),
      monthlyRevenue: Mock.Random.float(10000, 100000, 2, 2),
      todayOrders: Mock.Random.integer(10, 100),
      todayRevenue: Mock.Random.float(1000, 10000, 2, 2)
    })
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 轮播图管理
  'GET /service/banner': (req, res) => {
    console.log('🖼️ 轮播图列表API被调用:', req.url)
    const fullUrl = 'http://localhost:3000/api' + req.url
    const result = handlePagination(bannerList, fullUrl)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 分类管理
  'GET /service/category': (req, res) => {
    console.log('📂 分类列表API被调用:', req.url)
    if (req.url.includes('/tree')) {
      console.log('🌳 分类树API被调用')
      const result = successResponse(categoryTreeList)
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    } else {
      const fullUrl = 'http://localhost:3000/api' + req.url
      const result = handlePagination(categoryList, fullUrl)
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    }
  },

  'GET /service/category/tree': (req, res) => {
    console.log('🌳 分类树API被调用')
    const result = successResponse(categoryTreeList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 金刚区管理API
  'GET /service/jingang/list': (req, res) => {
    console.log('💎 金刚区列表API被调用')
    const jingangList = Mock.mock({
      'list|8': [{
        'id|+1': 1,
        'img': '@image("48x48", "@color", "#FFF", "png", "图标")',
        'title': '@ctitle(2, 4)',
        'link': '/pages/@word(5, 10)',
        'top|1-100': 50
      }]
    })
    const result = successResponse(jingangList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /service/jingang/add': (req, res) => {
    console.log('➕ 金刚区新增API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const newItem = JSON.parse(body)
      newItem.id = Mock.Random.integer(100, 999)
      const result = successResponse(newItem, '新增成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'PUT /service/jingang/update': (req, res) => {
    console.log('✏️ 金刚区更新API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const updateData = JSON.parse(body)
      const result = successResponse(updateData, '更新成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'DELETE /service/jingang/delete': (req, res) => {
    console.log('🗑️ 金刚区删除API被调用')
    const result = successResponse(null, '删除成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 服务点管理API
  'GET /service/daili/list': (req, res) => {
    console.log('🏪 服务点列表API被调用')
    const dailiList = Mock.mock({
      'list|10': [{
        'id|+1': 1,
        'img': '@image("48x48", "@color", "#FFF", "png", "封面")',
        'name': '@ctitle(4, 8)服务点',
        'address': '@county(true)',
        'tel': /^1[3-9]\d{9}$/,
        'service_cate': '@pick(["家政服务", "维修服务", "清洁服务", "搬家服务"])',
        'sun_code': '@image("48x48", "@color", "#FFF", "png", "二维码")',
        'is_grounding|1': [true, false]
      }]
    })
    const result = successResponse(dailiList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /service/daili/add': (req, res) => {
    console.log('➕ 服务点新增API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const newItem = JSON.parse(body)
      newItem.id = Mock.Random.integer(100, 999)
      const result = successResponse(newItem, '新增成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'PUT /service/daili/update': (req, res) => {
    console.log('✏️ 服务点更新API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const updateData = JSON.parse(body)
      const result = successResponse(updateData, '更新成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'PUT /service/daili/switch': (req, res) => {
    console.log('🔄 服务点状态切换API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const switchData = JSON.parse(body)
      const result = successResponse(switchData, '状态更新成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'DELETE /service/daili/delete': (req, res) => {
    console.log('🗑️ 服务点删除API被调用')
    const result = successResponse(null, '删除成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 项目配置管理API
  'GET /service/peizhi/list': (req, res) => {
    console.log('⚙️ 项目配置列表API被调用')
    const peizhiList = Mock.mock({
      'list|12': [{
        'id|+1': 1,
        'service_id|1-50': 1,
        'type|0-1': 0,
        'problem_desc': '@ctitle(4, 8)',
        'problem_content': '@ctitle(10, 20)',
        'input_type|1-4': 1,
        'is_required|0-1': 1,
        'options|2-5': ['@ctitle(2, 4)']
      }]
    })
    const result = successResponse(peizhiList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /service/peizhi/add': (req, res) => {
    console.log('➕ 项目配置新增API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const newItem = JSON.parse(body)
      newItem.id = Mock.Random.integer(100, 999)
      const result = successResponse(newItem, '新增成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'PUT /service/peizhi/update': (req, res) => {
    console.log('✏️ 项目配置更新API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const updateData = JSON.parse(body)
      const result = successResponse(updateData, '更新成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'DELETE /service/peizhi/delete': (req, res) => {
    console.log('🗑️ 项目配置删除API被调用')
    const result = successResponse(null, '删除成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // ==================== 一口价管理API ====================
  'GET /admin/oneprice/list': (req, res) => {
    console.log('📋 一口价列表Mock API被调用')

    // 内联Mock数据
    const onepriceList = [
      {
        id: 1,
        serviceId: 517,
        serviceTitle: '马桶安装',
        regionName: '全国',
        provinceId: null,
        cityId: null,
        areaId: null,
        num: 2,
        originalPrice: 87.00,
        price: 300.00,
        status: 1,
        priority: 12,
        effectiveBegin: '2025-08-22 00:00:00',
        effectiveEnd: '2025-08-22 23:59:59',
        configs: [
          {
            settingId: 1147,
            problemDesc: '马桶安装多选',
            values: ['多选1', '多选2']
          },
          {
            settingId: 1156,
            problemDesc: '马桶安装单选',
            values: ['单选1']
          }
        ],
        remark: '测试一口价配置'
      },
      {
        id: 2,
        serviceId: 518,
        serviceTitle: '水龙头维修',
        regionName: '安徽省-阜阳市-太和县',
        provinceId: 1001,
        cityId: 2001,
        areaId: 3001,
        num: 1,
        originalPrice: 120.00,
        price: 80.00,
        status: 1,
        priority: 10,
        effectiveBegin: '2025-08-22 00:00:00',
        effectiveEnd: '2025-08-22 23:59:59',
        configs: [],
        remark: '水龙头维修一口价'
      }
    ]

    try {
      const url = new URL('http://localhost:3000' + req.url)
      const pageNum = parseInt(url.searchParams.get('pageNum') || '1')
      const pageSize = parseInt(url.searchParams.get('pageSize') || '10')
      const serviceTitle = url.searchParams.get('serviceTitle')
      const status = url.searchParams.get('status')

      let filteredData = [...onepriceList]

      // 项目名称过滤
      if (serviceTitle) {
        filteredData = filteredData.filter(item =>
          item.serviceTitle.includes(serviceTitle)
        )
      }

      // 状态过滤
      if (status !== null && status !== '') {
        filteredData = filteredData.filter(item => item.status == status)
      }

      const start = (pageNum - 1) * pageSize
      const end = start + pageSize
      const list = filteredData.slice(start, end)

      const result = successResponse({
        list: list,
        totalCount: filteredData.length,
        totalPage: Math.ceil(filteredData.length / pageSize),
        pageNum: pageNum,
        pageSize: pageSize
      })

      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    } catch (error) {
      console.error('❌ 加载一口价Mock数据失败:', error)
      const result = errorResponse('加载数据失败')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    }
  },

  'GET /admin/oneprice/info': (req, res) => {
    console.log('🔍 一口价详情Mock API被调用')

    // 内联Mock数据（与列表API保持一致）
    const onepriceList = [
      {
        id: 1,
        serviceId: 517,
        serviceTitle: '马桶安装',
        regionName: '全国',
        provinceId: null,
        cityId: null,
        areaId: null,
        num: 2,
        originalPrice: 87.00,
        price: 300.00,
        status: 1,
        priority: 12,
        effectiveBegin: '2025-08-22 00:00:00',
        effectiveEnd: '2025-08-22 23:59:59',
        configs: [
          {
            settingId: 1147,
            problemDesc: '马桶安装多选',
            values: ['多选1', '多选2']
          },
          {
            settingId: 1156,
            problemDesc: '马桶安装单选',
            values: ['单选1']
          }
        ],
        remark: '测试一口价配置'
      }
    ]

    try {
      const id = parseInt(req.url.match(/\/(\d+)$/)[1])
      const item = onepriceList.find(item => item.id === id)

      if (item) {
        const result = successResponse(item)
        res.setHeader('Content-Type', 'application/json')
        res.end(JSON.stringify(result))
      } else {
        const result = errorResponse('一口价配置不存在', '404')
        res.setHeader('Content-Type', 'application/json')
        res.end(JSON.stringify(result))
      }
    } catch (error) {
      console.error('❌ 加载一口价详情失败:', error)
      const result = errorResponse('加载数据失败')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    }
  },

  'POST /admin/oneprice/add': (req, res) => {
    console.log('➕ 一口价新增Mock API被调用')

    let body = ''
    req.on('data', chunk => {
      body += chunk.toString()
    })

    req.on('end', () => {
      try {
        const data = JSON.parse(body)
        console.log('📤 新增数据:', data)

        const result = successResponse({
          id: Date.now(),
          ...data,
          createTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
          updateTime: new Date().toISOString().replace('T', ' ').substring(0, 19)
        }, '新增成功')

        res.setHeader('Content-Type', 'application/json')
        res.end(JSON.stringify(result))
      } catch (error) {
        console.error('❌ 解析请求数据失败:', error)
        const result = errorResponse('请求数据格式错误')
        res.setHeader('Content-Type', 'application/json')
        res.end(JSON.stringify(result))
      }
    })
  },

  'POST /admin/oneprice/update': (req, res) => {
    console.log('✏️ 一口价修改Mock API被调用')

    let body = ''
    req.on('data', chunk => {
      body += chunk.toString()
    })

    req.on('end', () => {
      try {
        const data = JSON.parse(body)
        console.log('📤 修改数据:', data)

        const result = successResponse({
          ...data,
          updateTime: new Date().toISOString().replace('T', ' ').substring(0, 19)
        }, '修改成功')

        res.setHeader('Content-Type', 'application/json')
        res.end(JSON.stringify(result))
      } catch (error) {
        console.error('❌ 解析请求数据失败:', error)
        const result = errorResponse('请求数据格式错误')
        res.setHeader('Content-Type', 'application/json')
        res.end(JSON.stringify(result))
      }
    })
  },

  'POST /admin/oneprice/delete': (req, res) => {
    console.log('🗑️ 一口价删除Mock API被调用')
    const result = successResponse(null, '删除成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /admin/oneprice/toggleStatus': (req, res) => {
    console.log('🔄 一口价状态切换Mock API被调用')

    let body = ''
    req.on('data', chunk => {
      body += chunk.toString()
    })

    req.on('end', () => {
      try {
        const data = JSON.parse(body)
        console.log('📤 状态切换数据:', data)

        const result = successResponse({
          id: data.id,
          status: data.status || 1,
          updateTime: new Date().toISOString().replace('T', ' ').substring(0, 19)
        }, '状态更新成功')

        res.setHeader('Content-Type', 'application/json')
        res.end(JSON.stringify(result))
      } catch (error) {
        console.error('❌ 解析请求数据失败:', error)
        const result = errorResponse('请求数据格式错误')
        res.setHeader('Content-Type', 'application/json')
        res.end(JSON.stringify(result))
      }
    })
  },

  'GET /admin/oneprice/priceSetting/list': (req, res) => {
    console.log('🔍 获取服务配置项Mock API被调用')

    // 内联配置项数据
    const serviceConfigList = [
      {
        id: 1147,
        type: 0,
        serviceId: 517,
        problemDesc: '马桶安装多选',
        problemContent: '测试马桶安装多选描述',
        isRequired: 1,
        inputType: 4,
        val: null,
        createTime: '2025-08-15 08:45:43',
        options: '["多选1","多选2","多选3","多选4","多选5"]'
      },
      {
        id: 1156,
        type: 0,
        serviceId: 517,
        problemDesc: '马桶安装单选',
        problemContent: '测试马桶安装单选描述',
        isRequired: 1,
        inputType: 3,
        val: null,
        createTime: '2025-08-21 09:09:03',
        options: '["单选1","单选2","单选3"]'
      }
    ]

    try {
      const url = new URL('http://localhost:3000' + req.url)
      const serviceId = url.searchParams.get('serviceId')

      if (!serviceId) {
        const result = errorResponse('服务ID不能为空', '400')
        res.setHeader('Content-Type', 'application/json')
        res.end(JSON.stringify(result))
        return
      }

      // 根据serviceId过滤配置项
      const filteredConfigs = serviceConfigList.filter(config =>
        config.serviceId == serviceId
      )

      const result = successResponse(filteredConfigs)
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    } catch (error) {
      console.error('❌ 加载配置项数据失败:', error)
      const result = errorResponse('加载数据失败')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    }
  },

  // 师傅管理API
  'GET /technician/list': (req, res) => {
    console.log('👨‍🔧 师傅列表API被调用')
    const technicianList = Mock.mock({
      'list|15': [{
        'id|+1': 1,
        'user_id|1-1000': 1,
        'coach_name': '@cname',
        'sex|0-1': 0,
        'sex_text': function() {
          return this.sex === 0 ? '男' : '女'
        },
        'mobile': /^1[3-9]\d{9}$/,
        'work_time|1-20': 5,
        'city_id': [1, 11],
        'address': '@county(true)',
        'lng': '@float(116, 117, 6, 6)',
        'lat': '@float(39, 40, 6, 6)',
        'text': '@cparagraph(1, 3)',
        'id_code': /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[0-9Xx]$/,
        'work_img': '@image("100x100", "@color", "#FFF", "png", "头像")',
        'label_name': '@pick(["初级师傅", "中级师傅", "高级师傅", "专家师傅"])',
        'create_time': '@datetime',
        'admin_add|0-1': 0,
        'status|1-3': 2,
        'is_enable|1-2': 1,
        'is_update|0-1': 0
      }],
      'count': {
        'all': 15,
        'ing': 3,
        'nopass': 2,
        'pass': 10
      }
    })
    const result = successResponse(technicianList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'GET /technician/detail': (req, res) => {
    console.log('👨‍🔧 师傅详情API被调用')
    const technicianDetail = Mock.mock({
      'id': '@integer(1, 100)',
      'user_id': '@integer(1, 1000)',
      'nickName': '@cname',
      'coach_name': '@cname',
      'sex|0-1': 0,
      'mobile': /^1[3-9]\d{9}$/,
      'work_time|1-20': 5,
      'city_id': [1, 11],
      'address': '@county(true)',
      'lng': '@float(116, 117, 6, 6)',
      'lat': '@float(39, 40, 6, 6)',
      'text': '@cparagraph(1, 3)',
      'id_code': /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[0-9Xx]$/,
      'id_card': ['@image("400x300")', '@image("400x300")', '@image("400x300")'],
      'self_img': ['@image("750x500")', '@image("750x500")'],
      'level_id': '@integer(1, 4)',
      'skills': [1, 2, 3],
      'status|1-3': 2,
      'is_enable|1-2': 1
    })
    const result = successResponse(technicianDetail)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /technician/add': (req, res) => {
    console.log('➕ 师傅新增API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const newItem = JSON.parse(body)
      newItem.id = Mock.Random.integer(100, 999)
      const result = successResponse(newItem, '新增成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'PUT /technician/update': (req, res) => {
    console.log('✏️ 师傅更新API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const updateData = JSON.parse(body)
      const result = successResponse(updateData, '更新成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'PUT /technician/toggle': (req, res) => {
    console.log('🔄 师傅状态切换API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const toggleData = JSON.parse(body)
      const result = successResponse(toggleData, '状态更新成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'DELETE /technician/delete': (req, res) => {
    console.log('🗑️ 师傅删除API被调用')
    const result = successResponse(null, '删除成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 师傅等级管理API
  'GET /technician/level/list': (req, res) => {
    console.log('📊 师傅等级列表API被调用')
    const levelList = Mock.mock({
      'list|6': [{
        'id|+1': 1,
        'label_name': '@pick(["初级师傅", "中级师傅", "高级师傅", "专家师傅", "金牌师傅", "钻石师傅"])',
        'create_time': '@datetime'
      }]
    })
    const result = successResponse(levelList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /technician/level/add': (req, res) => {
    console.log('➕ 师傅等级新增API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const newItem = JSON.parse(body)
      newItem.id = Mock.Random.integer(100, 999)
      const result = successResponse(newItem, '新增成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'PUT /technician/level/update': (req, res) => {
    console.log('✏️ 师傅等级更新API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const updateData = JSON.parse(body)
      const result = successResponse(updateData, '更新成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'DELETE /technician/level/delete': (req, res) => {
    console.log('🗑️ 师傅等级删除API被调用')
    const result = successResponse(null, '删除成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 用户列表API（供师傅编辑页面使用）
  'GET /technician/user/list': (req, res) => {
    console.log('👥 师傅用户列表API被调用')
    const userList = Mock.mock({
      'list|20': [{
        'id|+1': 1,
        'nickname': '@cname',
        'mobile': /^1[3-9]\d{9}$/,
        'create_time': '@datetime'
      }]
    })
    const result = successResponse(userList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 技能列表API
  'GET /technician/skill/list': (req, res) => {
    console.log('🛠️ 技能列表API被调用')
    const skillList = Mock.mock({
      'list|10': [{
        'id|+1': 1,
        'name': '@pick(["家电维修", "管道疏通", "电路维修", "空调清洗", "家具安装", "墙面粉刷", "防水补漏", "门锁维修", "灯具安装", "卫浴维修"])'
      }]
    })
    const result = successResponse(skillList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 师傅押金管理API
  'GET /technician/deposit/config': (req, res) => {
    console.log('💰 师傅押金配置获取API被调用')
    const depositConfig = Mock.mock({
      'cash_pledge': '@float(500, 2000, 0, 2)',
      'entry_notice': '@cparagraph(3, 5)',
      'commission_ratio': '@float(5, 20, 1, 2)'
    })
    const result = successResponse(depositConfig)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /technician/deposit/config': (req, res) => {
    console.log('💰 师傅押金配置保存API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const configData = JSON.parse(body)
      const result = successResponse(configData, '保存成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'GET /technician/deposit/records': (req, res) => {
    console.log('📋 师傅押金记录API被调用')
    const depositRecords = Mock.mock({
      'list|15': [{
        'id|+1': 1,
        'technician_id|1-100': 1,
        'technician_name': '@cname',
        'mobile': /^1[3-9]\d{9}$/,
        'deposit_amount': '@float(500, 2000, 0, 2)',
        'status|0-3': 1,
        'pay_time': '@datetime',
        'refund_time': function() {
          return this.status === 2 ? '@datetime' : null
        },
        'remark': '@csentence(5, 15)'
      }],
      'total': 15
    })
    const result = successResponse(depositRecords)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /technician/deposit/refund': (req, res) => {
    console.log('💸 师傅押金退还API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const refundData = JSON.parse(body)
      const result = successResponse(refundData, '退还成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  // 师傅接单距离管理API
  'GET /technician/distance/config': (req, res) => {
    console.log('📏 师傅接单距离配置获取API被调用')
    const distanceConfig = Mock.mock({
      'distance|5-20': 10
    })
    const result = successResponse(distanceConfig)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /technician/distance/config': (req, res) => {
    console.log('📏 师傅接单距离配置保存API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const configData = JSON.parse(body)
      const result = successResponse(configData, '保存成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'GET /technician/distance/statistics': (req, res) => {
    console.log('📊 师傅距离统计API被调用')
    const statistics = Mock.mock({
      'total|50-200': 120,
      'within_5km|10-50': 30,
      'within_10km|20-80': 60,
      'beyond_10km|10-70': 30
    })
    const result = successResponse(statistics)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 师傅服务区域管理API
  'GET /technician/area/list': (req, res) => {
    console.log('🗺️ 师傅服务区域列表API被调用')
    const areaList = Mock.mock({
      'list|8': [{
        'id|+1': 1,
        'name': '@city()@pick(["东区", "西区", "南区", "北区", "中心区"])',
        'center_address': '@county(true)',
        'radius|3-15': 8,
        'technician_count|5-50': 20,
        'status|0-1': 1,
        'create_time': '@datetime'
      }]
    })
    const result = successResponse(areaList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /technician/area/add': (req, res) => {
    console.log('➕ 师傅服务区域新增API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const newArea = JSON.parse(body)
      newArea.id = Mock.Random.integer(100, 999)
      newArea.technician_count = 0
      newArea.create_time = new Date().toISOString()
      const result = successResponse(newArea, '新增成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'PUT /technician/area/update': (req, res) => {
    console.log('✏️ 师傅服务区域更新API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const updateData = JSON.parse(body)
      const result = successResponse(updateData, '更新成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'PUT /technician/area/toggle': (req, res) => {
    console.log('🔄 师傅服务区域状态切换API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const toggleData = JSON.parse(body)
      const result = successResponse(toggleData, '状态更新成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'DELETE /technician/area/delete': (req, res) => {
    console.log('🗑️ 师傅服务区域删除API被调用')
    const result = successResponse(null, '删除成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 营销管理 - 卡券管理API
  'GET /api/admin/coupon/list': (req, res) => {
    console.log('🎫 卡券列表API被调用')
    const couponList = Mock.mock({
      'list|15': [{
        'id|+1': 1,
        'title': '@ctitle(4, 8)优惠券',
        'type|0-1': 0,
        'full': function() {
          return this.type === 0 ? '@float(50, 500, 0, 2)' : 0
        },
        'discount': '@float(5, 100, 0, 2)',
        'sendType|0-2': 0,  // 修改为驼峰命名
        'userLimit|1-2': 1, // 修改为驼峰命名
        'stock|50-1000': 100,
        'i|0-200': 50,      // 已领取数量
        'haveSend|0-100': 20, // 已使用数量
        'rule': '@cparagraph(2, 4)',
        'text': '@cparagraph(2, 4)',
        'timeLimit|0-1': 0, // 修改为驼峰命名
        'startTime': '@datetime', // 修改为驼峰命名
        'endTime': '@datetime',   // 修改为驼峰命名
        'day|1-30': 7,
        'top|0-100': 50,
        'status|0-1': 1,
        'createTime': '@datetime', // 修改为驼峰命名
        'serviceIds': [1, 2, 3]    // 修改为驼峰命名
      }],
      'total': 15
    })
    const result = successResponse(couponList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'GET /api/admin/coupon/detail': (req, res) => {
    console.log('🎫 卡券详情API被调用')
    const couponDetail = Mock.mock({
      'id': '@integer(1, 100)',
      'title': '@ctitle(4, 8)优惠券',
      'type|0-1': 0,
      'full': '@float(50, 500, 0, 2)',
      'discount': '@float(5, 100, 0, 2)',
      'sendType|0-2': 0,  // 修改为驼峰命名
      'userLimit|1-2': 1, // 修改为驼峰命名
      'stock|50-1000': 100,
      'i|0-200': 50,      // 已领取数量
      'haveSend|0-100': 20, // 已使用数量
      'rule': '@cparagraph(2, 4)',
      'text': '@cparagraph(2, 4)',
      'timeLimit|0-1': 0, // 修改为驼峰命名
      'startTime': '@datetime', // 修改为驼峰命名
      'endTime': '@datetime',   // 修改为驼峰命名
      'day|1-30': 7,
      'top|0-100': 50,
      'status|0-1': 1,
      'serviceIds': [1, 2, 3], // 修改为驼峰命名
      'services': [
        { id: 1, name: '家电维修', price: 100 },
        { id: 2, name: '管道疏通', price: 80 },
        { id: 3, name: '电路维修', price: 120 }
      ]
    })
    const result = successResponse(couponDetail)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /api/admin/coupon/add': (req, res) => {
    console.log('➕ 卡券新增API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const newCoupon = JSON.parse(body)
      newCoupon.id = Mock.Random.integer(100, 999)
      newCoupon.createTime = new Date().toISOString()
      const result = successResponse(newCoupon, '新增成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'POST /api/admin/coupon/edit': (req, res) => {
    console.log('✏️ 卡券更新API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const updateData = JSON.parse(body)
      const result = successResponse(updateData, '更新成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'POST /api/admin/coupon/status': (req, res) => {
    console.log('🔄 卡券状态切换API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const statusData = JSON.parse(body)
      const result = successResponse(statusData, '状态更新成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'POST /api/admin/coupon/delete': (req, res) => {
    console.log('🗑️ 卡券删除API被调用')
    const result = successResponse(null, '删除成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 营销管理 - 公告设置API
  'GET /market/activity/info': (req, res) => {
    console.log('🎯 活动信息获取API被调用')
    const activityInfo = Mock.mock({
      'status|0-1': 1,
      'share_img': '@image("652x652", "@color", "#FFF", "png", "分享图")',
      'time': ['@datetime', '@datetime'],
      'inv_user_num|1-10': 3,
      'inv_time|12-72': 24,
      'atv_num|1-5': 1,
      'inv_user': ['推荐人', '被推荐人'],
      'inv_reward_type|1-2': 1,
      'inv_points|10-100': 50,
      'inv_coupon_ids': [1, 2],
      'rec_reward_type|1-2': 1,
      'rec_points|10-100': 30,
      'rec_coupon_ids': [3, 4],
      'inv_coupons': [
        { id: 1, title: '推荐人专享券', discount: 20 },
        { id: 2, title: '邀请奖励券', discount: 15 }
      ],
      'rec_coupons': [
        { id: 3, title: '新人专享券', discount: 10 },
        { id: 4, title: '注册奖励券', discount: 5 }
      ]
    })
    const result = successResponse(activityInfo)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /market/activity/save': (req, res) => {
    console.log('💾 活动保存API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const activityData = JSON.parse(body)
      const result = successResponse(activityData, '保存成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  // 营销管理 - 公告设置API
  'GET /market/notice/list': (req, res) => {
    console.log('📢 通知列表API被调用')
    const noticeList = Mock.mock({
      'list|12': [{
        'id|+1': 1,
        'content': '@cparagraph(1, 3)',
        'create_time': '@datetime'
      }],
      'total': 12
    })
    const result = successResponse(noticeList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /market/notice/add': (req, res) => {
    console.log('➕ 通知新增API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const newNotice = JSON.parse(body)
      newNotice.id = Mock.Random.integer(100, 999)
      newNotice.create_time = new Date().toISOString()
      const result = successResponse(newNotice, '新增成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'PUT /market/notice/update': (req, res) => {
    console.log('✏️ 通知更新API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const updateData = JSON.parse(body)
      const result = successResponse(updateData, '更新成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'DELETE /market/notice/delete': (req, res) => {
    console.log('🗑️ 通知删除API被调用')
    const result = successResponse(null, '删除成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 订单管理API
  'GET /shop/order/list': (req, res) => {
    console.log('📋 订单列表API被调用')
    const orderList = Mock.mock({
      'list|20': [{
        'id|+1': 1,
        'order_code': '@string("upper", 2)@string("number", 8)',
        'user_name': '@cname',
        'user_mobile': /^1[3-9]\d{9}$/,
        'address_info': '@county(true)',
        'appointment_time': '@datetime',
        'create_time': '@datetime',
        'pay_type|1-7': 2,
        'pay_method|1-3': 1,
        'goods_price': '@float(50, 500, 0, 2)',
        'discount_price': '@float(0, 50, 0, 2)',
        'pay_price': function() {
          return (this.goods_price - this.discount_price).toFixed(2)
        },
        'order_goods|1-3': [{
          'goods_id|+1': 1,
          'goods_name': '@pick(["家电维修", "管道疏通", "电路维修", "空调清洗", "家具安装"])',
          'goods_cover': '@image("200x200", "@color", "#FFF", "png", "项目")',
          'goods_price': '@float(50, 200, 0, 2)',
          'num|1-3': 1
        }],
        'coach_info': {
          'coach_id|1-100': 1,
          'coach_name': '@cname',
          'mobile': /^1[3-9]\d{9}$/,
          'level_name': '@pick(["初级师傅", "中级师傅", "高级师傅"])',
          'work_time|1-20': 5
        }
      }],
      'total': 20,
      'count': {
        'all': 20,
        'unpaid': 3,
        'pending': 5,
        'accepted': 4,
        'serving': 3,
        'completed': 2,
        'finished': 2,
        'cancelled': 1
      }
    })
    const result = successResponse(orderList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'GET /shop/order/detail': (req, res) => {
    console.log('📋 订单详情API被调用')
    const orderDetail = Mock.mock({
      'id': '@integer(1, 100)',
      'order_code': '@string("upper", 2)@string("number", 8)',
      'user_name': '@cname',
      'user_mobile': /^1[3-9]\d{9}$/,
      'address_info': '@county(true)',
      'appointment_time': '@datetime',
      'create_time': '@datetime',
      'pay_type|1-7': 2,
      'pay_method|1-3': 1,
      'goods_price': '@float(100, 500, 0, 2)',
      'discount_price': '@float(0, 50, 0, 2)',
      'pay_price': function() {
        return (this.goods_price - this.discount_price).toFixed(2)
      },
      'order_goods|1-3': [{
        'goods_id|+1': 1,
        'goods_name': '@pick(["家电维修", "管道疏通", "电路维修", "空调清洗", "家具安装"])',
        'goods_cover': '@image("200x200", "@color", "#FFF", "png", "项目")',
        'goods_price': '@float(50, 200, 0, 2)',
        'num|1-3': 1
      }],
      'coach_info': {
        'coach_id|1-100': 1,
        'coach_name': '@cname',
        'mobile': /^1[3-9]\d{9}$/,
        'level_name': '@pick(["初级师傅", "中级师傅", "高级师傅"])',
        'work_time|1-20': 5
      },
      'service_logs|0-5': [{
        'title': '@pick(["开始服务", "服务进行中", "服务完成", "问题处理"])',
        'content': '@cparagraph(1, 2)',
        'create_time': '@datetime',
        'images|0-3': ['@image("300x200", "@color", "#FFF", "png", "服务")']
      }]
    })
    const result = successResponse(orderDetail)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /shop/order/cancel': (req, res) => {
    console.log('❌ 订单取消API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const cancelData = JSON.parse(body)
      const result = successResponse(cancelData, '订单取消成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'PUT /shop/order/complete': (req, res) => {
    console.log('✅ 订单完成API被调用')
    const result = successResponse(null, '订单完成成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 退款管理API
  'GET /shop/refund/list': (req, res) => {
    console.log('💰 退款列表API被调用')
    const refundList = Mock.mock({
      'list|15': [{
        'id|+1': 1,
        'refund_code': 'RF@string("number", 10)',
        'order_code': '@string("upper", 2)@string("number", 8)',
        'user_name': '@cname',
        'user_mobile': /^1[3-9]\d{9}$/,
        'refund_amount': '@float(50, 500, 0, 2)',
        'refund_reason': '@pick(["服务不满意", "师傅未按时到达", "服务质量问题", "个人原因", "其他原因"])',
        'create_time': '@datetime',
        'status|1-4': 1,
        'handle_time': '@datetime',
        'reject_reason': ''
      }],
      'total': 15,
      'count': {
        'all': 15,
        'pending': 5,
        'approved': 4,
        'rejected': 3,
        'refunded': 3
      }
    })
    const result = successResponse(refundList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'GET /shop/refund/detail': (req, res) => {
    console.log('💰 退款详情API被调用')
    const refundDetail = Mock.mock({
      'id': '@integer(1, 100)',
      'refund_code': 'RF@string("number", 10)',
      'order_code': '@string("upper", 2)@string("number", 8)',
      'user_name': '@cname',
      'user_mobile': /^1[3-9]\d{9}$/,
      'refund_amount': '@float(50, 500, 0, 2)',
      'refund_reason': '@cparagraph(1, 2)',
      'create_time': '@datetime',
      'status|1-4': 1,
      'refund_images|0-3': ['@image("300x200", "@color", "#FFF", "png", "凭证")']
    })
    const result = successResponse(refundDetail)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /shop/refund/approve': (req, res) => {
    console.log('✅ 退款同意API被调用')
    const result = successResponse(null, '退款申请已同意')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /shop/refund/reject': (req, res) => {
    console.log('❌ 退款拒绝API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const rejectData = JSON.parse(body)
      const result = successResponse(rejectData, '退款申请已拒绝')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  // 分销管理API
  'GET /distribution/examine/list': (req, res) => {
    console.log('👥 分销审核列表API被调用')
    const distributionList = Mock.mock({
      'list|18': [{
        'id|+1': 1,
        'user_id|1000-9999': 1000,
        'avatarUrl': '@image("100x100", "@color", "#FFF", "png", "头像")',
        'nickName': '@cname',
        'user_name': '@cname',
        'mobile': /^1[3-9]\d{9}$/,
        'id_code': /^\d{17}[\dX]$/,
        'create_time': '@datetime',
        'status|1-4': 1,
        'commission_total': '@float(0, 5000, 0, 2)',
        'invite_count|0-50': 10,
        'reject_reason': ''
      }],
      'total': 18,
      'count': {
        'all': 18,
        'nopass': 3,
        'ing': 8,
        'pass': 5,
        'reject': 2
      }
    })
    const result = successResponse(distributionList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'GET /distribution/examine/detail': (req, res) => {
    console.log('👥 分销商详情API被调用')
    const distributorDetail = Mock.mock({
      'id': '@integer(1, 100)',
      'user_id|1000-9999': 1000,
      'avatarUrl': '@image("100x100", "@color", "#FFF", "png", "头像")',
      'nickName': '@cname',
      'user_name': '@cname',
      'mobile': /^1[3-9]\d{9}$/,
      'id_code': /^\d{17}[\dX]$/,
      'create_time': '@datetime',
      'status|1-4': 1,
      'commission_total': '@float(0, 5000, 0, 2)',
      'invite_count|0-50': 10,
      'id_card_images|2': ['@image("300x200", "@color", "#FFF", "png", "身份证")']
    })
    const result = successResponse(distributorDetail)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /distribution/examine/approve': (req, res) => {
    console.log('✅ 分销审核通过API被调用')
    const result = successResponse(null, '审核通过成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /distribution/examine/reject': (req, res) => {
    console.log('❌ 分销审核拒绝API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const rejectData = JSON.parse(body)
      const result = successResponse(rejectData, '审核拒绝成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'PUT /distribution/examine/audit': (req, res) => {
    console.log('📋 分销审核API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const auditData = JSON.parse(body)
      const result = successResponse(auditData, '审核成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  // 分销商列表管理API
  'GET /distribution/list': (req, res) => {
    console.log('👥 分销商列表API被调用')
    const distributorList = Mock.mock({
      'list|20': [{
        'id|+1': 1,
        'user_id|1000-9999': 1000,
        'avatarUrl': '@image("100x100", "@color", "#FFF", "png", "头像")',
        'user_name': '@cname',
        'mobile': /^1[3-9]\d{9}$/,
        'level|1-3': 1,
        'commission_total': '@float(0, 10000, 0, 2)',
        'invite_count|0-100': 20,
        'order_count|0-50': 10,
        'last_active_time': '@datetime',
        'create_time': '@datetime',
        'status|1-2': 1
      }],
      'total': 20,
      'stats': {
        'total_distributors': '@integer(100, 500)',
        'active_distributors': '@integer(50, 200)',
        'total_commission': '@float(10000, 100000, 0, 2)',
        'total_invites': '@integer(500, 2000)'
      }
    })
    const result = successResponse(distributorList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'GET /distribution/detail': (req, res) => {
    console.log('👤 分销商详情API被调用')
    const distributorDetail = Mock.mock({
      'id': '@integer(1, 100)',
      'user_id|1000-9999': 1000,
      'avatarUrl': '@image("100x100", "@color", "#FFF", "png", "头像")',
      'user_name': '@cname',
      'mobile': /^1[3-9]\d{9}$/,
      'level|1-3': 1,
      'commission_total': '@float(0, 10000, 0, 2)',
      'invite_count|0-100': 20,
      'order_count|0-50': 10,
      'last_active_time': '@datetime',
      'create_time': '@datetime',
      'status|1-2': 1
    })
    const result = successResponse(distributorDetail)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /distribution/status': (req, res) => {
    console.log('🔄 分销商状态切换API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const statusData = JSON.parse(body)
      const result = successResponse(statusData, '状态更新成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  // 分销设置API
  'GET /distribution/config': (req, res) => {
    console.log('⚙️ 分销配置获取API被调用')
    const distributionConfig = Mock.mock({
      'fx_check|0-1': 1,
      'cash_one': '@float(0, 20, 0, 2)',
      'cash_two': '@float(0, 10, 0, 2)',
      'apply_condition|1-3': 1,
      'min_amount': '@float(100, 1000, 0, 2)',
      'settlement_type|1-3': 1,
      'settlement_day|1-28': 15,
      'min_withdraw': '@float(10, 100, 0, 2)',
      'withdraw_fee': '@float(0, 5, 0, 2)',
      'poster_image': '@image("750x1334", "@color", "#FFF", "png", "海报")',
      'agreement': '@cparagraph(5, 10)'
    })
    const result = successResponse(distributionConfig)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /distribution/config': (req, res) => {
    console.log('💾 分销配置保存API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const configData = JSON.parse(body)
      const result = successResponse(configData, '保存成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  // 财务管理API
  'GET /finance/list': (req, res) => {
    console.log('💰 财务列表API被调用')
    const financeList = Mock.mock({
      'list|15': [{
        'coach_id|+1': 1,
        'coach_name': '@cname',
        'coach_mobile': /^1[3-9]\d{9}$/,
        'order_price': '@float(1000, 10000, 0, 2)',
        'order_count|10-100': 50,
        'total_price': '@float(500, 5000, 0, 2)',
        'total_count|5-50': 20,
        'wallet_price': '@float(800, 8000, 0, 2)',
        'wallet_count|8-80': 40,
        'service_car_price': '@float(100, 2000, 0, 2)',
        'update_time': '@datetime'
      }],
      'total': 15,
      'stats': {
        'total_income': '@float(50000, 100000, 0, 2)',
        'total_withdraw': '@float(20000, 40000, 0, 2)',
        'total_balance': '@float(10000, 30000, 0, 2)',
        'total_orders': '@integer(500, 2000)'
      }
    })
    const result = successResponse(financeList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'GET /finance/export': (req, res) => {
    console.log('📊 财务数据导出API被调用')
    const result = successResponse({ download_url: '/downloads/finance_export.xlsx' }, '导出成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 财务详情API
  'GET /finance/detail': (req, res) => {
    console.log('📋 财务详情API被调用')
    const financeDetail = Mock.mock({
      'list|20': [{
        'id|+1': 1,
        'date': '@datetime',
        'type|1-3': 1,
        'order_price': '@float(0, 500, 0, 2)',
        'refund_price': '@float(0, 100, 0, 2)',
        'wallet_price': '@float(0, 200, 0, 2)',
        'balance': '@float(100, 2000, 0, 2)',
        'remark': '@pick(["订单收入", "订单退款", "提现", "系统调整"])'
      }],
      'total': 20,
      'info': {
        'coach_name': '@cname',
        'total_income': '@float(5000, 20000, 0, 2)',
        'total_refund': '@float(500, 2000, 0, 2)',
        'total_withdraw': '@float(1000, 5000, 0, 2)',
        'current_balance': '@float(1000, 10000, 0, 2)'
      }
    })
    const result = successResponse(financeDetail)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'GET /finance/export-detail': (req, res) => {
    console.log('📊 财务详情导出API被调用')
    const result = successResponse({ download_url: '/downloads/finance_detail_export.xlsx' }, '导出成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 提现管理API - 根据用户提供的接口规范重新实现
  'GET /api/admin/wallet/list': (req, res) => {
    console.log('💸 提现管理列表API被调用')
    const withdrawList = Mock.mock({
      'list|15': [{
        'id|+1': 1,
        'code': '20250614154849419luYsAQ5FgpQM6cH',
        'userId|1000-99999': 11572,
        'coachId|1000-9999': 1332,
        'applyPrice': '@float(1, 1000, 0, 1)',
        'servicePrice': '@float(0, 10, 0, 1)',
        'balance|50-200': 100,
        'truePrice': function() {
          return this.status === 2 ? this.applyPrice : (this.status === -1 ? 0 : this.applyPrice)
        },
        'status|1': [-1, 1, 2, 3, 4],
        'createTime': '@datetime("yyyy-MM-dd HH:mm:ss")',
        'shTime': function() {
          return this.status > 1 ? '@datetime("yyyy-MM-dd HH:mm:ss")' : null
        },
        'type|1-4': 2,
        'online|0-1': 0,
        'text': function() {
          const statusTexts = {
            '-1': '{"code":"NOT_ENOUGH","message":"商户运营账户资金不足，充值后可以原单号发起重试，请勿更换商户单号"}',
            '1': 'PENDING',
            '2': 'SUCCESS',
            '3': 'OVERDUE_CLOSE',
            '4': 'CLOSED'
          }
          return statusTexts[this.status] || 'SUCCESS'
        },
        'adminId|0-10': 0,
        'lock|0-2': 1,
        'sourceType|1-2': 2,
        'cashToType|1-3': 1,
        'friendlyFailReason': function() {
          if (this.status === -1) return '@pick(["商户运营账户资金不足", "非法请求提现", "提现金额异常"])'
          if (this.status === 3) return '超时未领取，系统已退还'
          return null
        }
      }],
      'totalCount': 24,
      'totalPage': 3,
      'pageNum': 1,
      'pageSize': 10
    })
    const result = successResponse(withdrawList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'GET /api/admin/wallet/stats': (req, res) => {
    console.log('📊 提现汇总统计API被调用')
    const stats = Mock.mock({
      'totalCount': 24,
      'totalApply': '@float(500, 1000, 0, 2)',
      'totalService': '@float(0, 50, 0, 2)',
      'totalArrived': '@float(10, 100, 0, 2)',
      'withdrawedCount|0-5': 1,
      'arrivedCount|5-15': 8,
      'failedCount|10-20': 15
    })
    const result = successResponse(stats)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'GET /api/admin/wallet/export': (req, res) => {
    console.log('📤 提现管理导出Excel API被调用，查询参数:', req.url)

    // 模拟Excel文件内容（CSV格式，浏览器会自动处理）
    const csvContent = `\uFEFF提现ID,提现单号,用户ID,师傅ID,申请金额,手续费,实际金额,状态,类型,创建时间,审核状态,失败原因
1,20250614154849419luYsAQ5FgpQM6cH,11572,1332,1.0,0.0,1.0,失败,服务费,2025-06-14 07:48:49,审核通过,超时未领取
2,20250601181827223vSrXmpe1a0UvH2j,11535,0,1.0,0.0,1.0,到账,用户分销,2025-06-01 10:18:27,审核通过,
3,20250601180354498cgOv2bM7LlDRlYo,11537,1311,5.0,0.0,0.0,内部错误,服务费,2025-06-01 10:03:54,审核通过,商户运营账户资金不足
4,20250601174406275i03KOsFLDZuEoSG,11537,1311,5.0,0.0,5.0,到账,服务费,2025-06-01 09:44:06,审核通过,
5,20250531161346284I9POwLXf8ur9hun,11291,,501.0,0.0,0.0,内部错误,车费,2025-05-31 08:13:46,审核通过,非法请求提现
6,20250530091403896WDfF2hYYwy5UBIy,1057,1212,1.0,0.0,1.0,到账,用户分销,2025-05-30 01:14:03,审核通过,
7,20250530091114939uEGGsKAoxYZOdiq,11291,1216,1.0,0.0,1.0,到账,服务费,2025-05-30 01:11:14,审核通过,
8,20250530083125435LM8RaSDznHifFjk,11536,1298,0.0,0.0,0.0,内部错误,服务费,2025-05-30 00:31:25,审核通过,提现金额异常
9,20250529215502482DSKMbIsLXZNLib4,11291,1216,1.0,0.0,1.0,失败,服务费,2025-05-29 13:55:02,审核通过,超时未领取
10,20250528143256789AbCdEfGhIjKlMn,11234,1345,50.0,2.5,47.5,到账,车费,2025-05-28 14:32:56,审核通过,`

    // 设置响应头为CSV文件下载（Excel可以打开CSV文件）
    res.setHeader('Content-Type', 'text/csv;charset=utf-8')
    res.setHeader('Content-Disposition', `attachment; filename="withdraw_export_${new Date().toISOString().slice(0, 10)}.csv"`)
    res.setHeader('Access-Control-Allow-Origin', '*')
    res.setHeader('Access-Control-Allow-Headers', '*')
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')

    // 返回CSV内容
    res.end(csvContent)
  },

  // 历史提现管理API - 根据用户提供的接口规范实现
  'GET /admin/wallet/history/page': (req, res) => {
    console.log('🎉🎉🎉 历史提现列表Mock API被成功调用！')
    console.log('📋 请求URL:', req.url)
    console.log('📋 请求参数:', req.query)

    // 生成Mock数据
    const mockData = []
    for (let i = 0; i < 20; i++) {
      const type = Mock.Random.integer(1, 4)
      const status = Mock.Random.pick([-1, 1, 2, 3, 4])

      const typeMap = {
        1: '车费',
        2: '服务费',
        3: '加盟',
        4: '用户分销'
      }

      const statusMap = {
        '-1': '内部错误',
        1: '已提现，未领取',
        2: '到账',
        3: '失败',
        4: '关闭'
      }

      let failReason = null
      let failFriendlyReason = ''

      if (status === -1) {
        failReason = '商户运营账户资金不足'
        failFriendlyReason = '系统繁忙，请稍后重试'
      } else if (status === 3) {
        failReason = '提现失败，请重试'
        failFriendlyReason = '网络异常，提现失败'
      }

      mockData.push({
        outBillNo: `20250721${Mock.Random.string('number', 10)}${Mock.Random.string('upper', 10)}`,
        userId: Mock.Random.integer(1000, 99999),
        type: type,
        typeText: typeMap[type],
        status: status,
        statusText: statusMap[status],
        failReason: failReason,
        failFriendlyReason: failFriendlyReason,
        changeTime: Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
        remark: Mock.Random.pick(['发起提现', '系统处理中', '提现成功', '提现失败', '用户取消'])
      })
    }

    const historyList = {
      list: mockData,
      totalCount: 53,
      totalPage: 6,
      pageNum: 1,
      pageSize: 10
    }

    const result = successResponse(historyList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'GET /admin/wallet/history/export': (req, res) => {
    console.log('📤 历史提现列表导出API被调用，查询参数:', req.url)

    // 生成CSV格式的导出数据
    const headers = [
      '订单号',
      '用户ID',
      '提现类型',
      '状态',
      '变更时间',
      '备注',
      '失败原因'
    ]

    // 生成示例数据
    const rows = []
    for (let i = 0; i < 50; i++) {
      const type = Mock.Random.integer(1, 4)
      const status = Mock.Random.pick([-1, 1, 2, 3, 4])

      const typeMap = {
        1: '车费',
        2: '服务费',
        3: '加盟',
        4: '用户分销'
      }

      const statusMap = {
        '-1': '内部错误',
        1: '已提现，未领取',
        2: '到账',
        3: '失败',
        4: '关闭'
      }

      let failReason = ''
      if (status === -1) {
        failReason = '商户运营账户资金不足'
      } else if (status === 3) {
        failReason = '提现失败，请重试'
      }

      rows.push([
        `20250721${Mock.Random.string('number', 10)}${Mock.Random.string('upper', 10)}`,
        Mock.Random.integer(1000, 99999),
        typeMap[type],
        statusMap[status],
        Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
        Mock.Random.pick(['发起提现', '系统处理中', '提现成功', '提现失败', '用户取消']),
        failReason
      ])
    }

    // 构建CSV内容
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n')

    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.ms-excel;charset=utf-8')
    res.setHeader('Content-Disposition', `attachment; filename="withdraw_history_export_${new Date().toISOString().slice(0, 10)}.csv"`)
    res.setHeader('Access-Control-Allow-Origin', '*')

    // 添加BOM以支持中文
    const BOM = '\uFEFF'
    res.end(BOM + csvContent)
  },

  'POST /api/admin/wallet/audit': (req, res) => {
    console.log('✅ 审核提现记录API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const auditData = JSON.parse(body)
      console.log('审核数据:', auditData)
      const result = successResponse(null, '审核成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'GET /api/admin/wallet/operatorBalance': (req, res) => {
    console.log('💰 查询运营账户余额API被调用')
    const balance = Mock.mock('@float(0, 10000, 0, 2)')
    const result = successResponse(balance.toString())
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /api/admin/wallet/operatorBalance/set': (req, res) => {
    console.log('💰 设置运营账户余额API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const setData = JSON.parse(body)
      console.log('设置余额数据:', setData)
      const result = successResponse(null, '设置成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'POST /api/admin/wallet/operatorBalance/increase': (req, res) => {
    console.log('💰 增加运营账户余额API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const increaseData = JSON.parse(body)
      console.log('增加余额数据:', increaseData)
      const result = successResponse(null, '增加成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'POST /api/admin/wallet/operatorBalance/decrease': (req, res) => {
    console.log('💰 扣减运营账户余额API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const decreaseData = JSON.parse(body)
      console.log('扣减余额数据:', decreaseData)
      const result = successResponse(null, '扣减成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },



  // 储值管理API
  'GET /finance/stored/list': (req, res) => {
    console.log('💰 储值列表API被调用')
    const storedList = Mock.mock({
      'list|20': [{
        'id|+1': 1,
        'avatarUrl': '@image("100x100", "@color", "#FFF", "png", "头像")',
        'nickName': '@cname',
        'mobile': /^1[3-9]\d{9}$/,
        'total_recharge': '@float(100, 5000, 0, 2)',
        'total_consume': '@float(50, 3000, 0, 2)',
        'current_balance': function() {
          return (this.total_recharge - this.total_consume).toFixed(2)
        },
        'recharge_count|1-20': 5,
        'last_recharge_time': '@datetime',
        'create_time': '@datetime'
      }],
      'total': 20,
      'stats': {
        'total_users': '@integer(100, 1000)',
        'total_amount': '@float(10000, 100000, 0, 2)',
        'used_amount': '@float(5000, 50000, 0, 2)',
        'balance_amount': '@float(5000, 50000, 0, 2)'
      }
    })
    const result = successResponse(storedList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'GET /finance/stored/detail': (req, res) => {
    console.log('💰 储值详情API被调用')
    const storedDetail = Mock.mock({
      'id': '@integer(1, 100)',
      'avatarUrl': '@image("100x100", "@color", "#FFF", "png", "头像")',
      'nickName': '@cname',
      'mobile': /^1[3-9]\d{9}$/,
      'total_recharge': '@float(100, 5000, 0, 2)',
      'total_consume': '@float(50, 3000, 0, 2)',
      'current_balance': function() {
        return (this.total_recharge - this.total_consume).toFixed(2)
      },
      'recharge_count|1-20': 5,
      'last_recharge_time': '@datetime',
      'create_time': '@datetime'
    })
    const result = successResponse(storedDetail)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /finance/stored/adjust': (req, res) => {
    console.log('⚖️ 余额调整API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const adjustData = JSON.parse(body)
      const result = successResponse(adjustData, '余额调整成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'GET /finance/stored/export': (req, res) => {
    console.log('📊 储值数据导出API被调用')
    const result = successResponse({ download_url: '/downloads/stored_export.xlsx' }, '导出成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 用户管理API
  'GET /user/list': (req, res) => {
    console.log('👤 用户列表API被调用')
    const userList = Mock.mock({
      'list|20': [{
        'id|+1': 1,
        'nickName': '@cname',
        'avatarUrl': '@image("100x100", "@color", "#FFF", "png", "头像")',
        'mobile': /^1[3-9]\d{9}$/,
        'gender|0-2': 1,
        'order_count|0-50': 10,
        'total_amount': '@float(0, 5000, 0, 2)',
        'last_login_time': '@datetime',
        'create_time': '@datetime',
        'status|0-1': 1,
        'is_message_customer|0-1': 0
      }],
      'total': 20,
      'stats': {
        'total_users': '@integer(1000, 5000)',
        'today_new': '@integer(10, 100)',
        'active_users': '@integer(500, 2000)',
        'vip_users': '@integer(50, 500)'
      }
    })
    const result = successResponse(userList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'GET /user/detail': (req, res) => {
    console.log('👤 用户详情API被调用')
    const userDetail = Mock.mock({
      'info': {
        'id': '@integer(1, 100)',
        'nickName': '@cname',
        'avatarUrl': '@image("100x100", "@color", "#FFF", "png", "头像")',
        'mobile': /^1[3-9]\d{9}$/,
        'gender|0-2': 1,
        'create_time': '@datetime',
        'last_login_time': '@datetime',
        'status|1-2': 1,
        'type|1-2': 1,
        'address': '@county(true)'
      },
      'stats': {
        'order_count|0-50': 10,
        'total_amount': '@float(100, 5000, 0, 2)',
        'balance': '@float(0, 1000, 0, 2)',
        'coupon_count|0-20': 5
      },
      'recent_orders|5': [{
        'id|+1': 1,
        'order_code': '@string("upper", 2)@string("number", 8)',
        'service_name': '@pick(["家电维修", "管道疏通", "家政清洁", "搬家服务", "装修服务"])',
        'total_price': '@float(50, 500, 0, 2)',
        'status|1-5': 2,
        'create_time': '@datetime'
      }]
    })
    const result = successResponse(userDetail)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /user/customer-service': (req, res) => {
    console.log('🎧 用户客服设置API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const serviceData = JSON.parse(body)
      const result = successResponse(serviceData, '设置成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'PUT /user/status': (req, res) => {
    console.log('🔄 用户状态切换API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const statusData = JSON.parse(body)
      const result = successResponse(statusData, '状态更新成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  // 评价管理API
  'GET /shop/evaluate/list': (req, res) => {
    console.log('⭐ 评价列表API被调用')
    const evaluateList = Mock.mock({
      'list|15': [{
        'id|+1': 1,
        'user_id|1000-9999': 1000,
        'user_name': '@cname',
        'coach_name': '@cname',
        'star|1-5': 5,
        'text': '@cparagraph(1, 3)',
        'reply_text': function() {
          return Math.random() > 0.5 ? '@cparagraph(1, 2)' : ''
        },
        'create_time': '@datetime',
        'images|0-3': ['@image("200x200", "@color", "#FFF", "png", "评价")'],
        'order_goods|1-2': [{
          'goods_name': '@pick(["家电维修", "管道疏通", "电路维修", "空调清洗", "家具安装"])',
          'goods_cover': '@image("200x200", "@color", "#FFF", "png", "项目")',
          'price': '@float(50, 200, 0, 2)',
          'goods_num|1-3': 1
        }]
      }],
      'total': 15
    })
    const result = successResponse(evaluateList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'GET /shop/evaluate/detail': (req, res) => {
    console.log('⭐ 评价详情API被调用')
    const evaluateDetail = Mock.mock({
      'id': '@integer(1, 100)',
      'user_id|1000-9999': 1000,
      'user_name': '@cname',
      'coach_name': '@cname',
      'star|1-5': 5,
      'text': '@cparagraph(2, 4)',
      'reply_text': '@cparagraph(1, 2)',
      'create_time': '@datetime',
      'images|0-3': ['@image("300x200", "@color", "#FFF", "png", "评价")']
    })
    const result = successResponse(evaluateDetail)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /shop/evaluate/reply': (req, res) => {
    console.log('💬 评价回复API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const replyData = JSON.parse(body)
      const result = successResponse(replyData, '回复成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'DELETE /shop/evaluate/delete': (req, res) => {
    console.log('🗑️ 评价删除API被调用')
    const result = successResponse(null, '删除成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 分销佣金API
  'GET /shop/commission/list': (req, res) => {
    console.log('💰 佣金列表API被调用')
    const commissionList = Mock.mock({
      'list|20': [{
        'id|+1': 1,
        'top_name': '@cname',
        'mobile': /^1[3-9]\d{9}$/,
        'order_code': '@string("upper", 2)@string("number", 8)',
        'goods_name': '@pick(["家电维修", "管道疏通", "电路维修", "空调清洗", "家具安装"])',
        'order_price': '@float(100, 1000, 0, 2)',
        'commission_rate|5-20': 10,
        'commission_amount': function() {
          return (this.order_price * this.commission_rate / 100).toFixed(2)
        },
        'level|1-2': 1,
        'status|1-3': 1,
        'create_time': '@datetime'
      }],
      'total': 20,
      'stats': {
        'total_cash': '@float(1000, 10000, 0, 2)',
        'unrecorded_cash': '@float(500, 5000, 0, 2)',
        'wallet_cash': '@float(500, 5000, 0, 2)'
      }
    })
    const result = successResponse(commissionList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /shop/commission/settle': (req, res) => {
    console.log('💸 佣金结算API被调用')
    const result = successResponse(null, '佣金结算成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /shop/commission/withdraw': (req, res) => {
    console.log('💳 佣金提现API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const withdrawData = JSON.parse(body)
      const result = successResponse(withdrawData, '提现申请提交成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  // 售后管理API
  'GET /shop/aftersale/list': (req, res) => {
    console.log('🔧 售后列表API被调用')
    const afterSaleList = Mock.mock({
      'list|12': [{
        'id|+1': 1,
        'brand': '@pick(["海尔", "美的", "格力", "小米", "华为"])',
        'title': '@pick(["空调维修", "洗衣机维修", "冰箱维修", "电视维修", "热水器维修"])',
        'time': '@datetime',
        'name': '@cname',
        'tel': /^1[3-9]\d{9}$/,
        'mark': '@cparagraph(1, 2)',
        'status|1-4': 1
      }],
      'total': 12
    })
    const result = successResponse(afterSaleList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'GET /shop/aftersale/detail': (req, res) => {
    console.log('🔧 售后详情API被调用')
    const afterSaleDetail = Mock.mock({
      'id': '@integer(1, 100)',
      'brand': '@pick(["海尔", "美的", "格力", "小米", "华为"])',
      'title': '@pick(["空调维修", "洗衣机维修", "冰箱维修", "电视维修", "热水器维修"])',
      'time': '@datetime',
      'name': '@cname',
      'tel': /^1[3-9]\d{9}$/,
      'mark': '@cparagraph(2, 4)',
      'status|1-4': 1,
      'images|0-3': ['@image("300x200", "@color", "#FFF", "png", "问题")']
    })
    const result = successResponse(afterSaleDetail)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /shop/aftersale/handle': (req, res) => {
    console.log('🛠️ 售后处理API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const handleData = JSON.parse(body)
      const result = successResponse(handleData, '处理成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'DELETE /shop/aftersale/delete': (req, res) => {
    console.log('🗑️ 售后删除API被调用')
    const result = successResponse(null, '删除成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 系统配置API
  'GET /system/config': (req, res) => {
    console.log('⚙️ 系统配置获取API被调用')
    const systemConfig = Mock.mock({
      'service_mobile': /^1[3-9]\d{9}$/
    })
    const result = successResponse(systemConfig)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /system/config': (req, res) => {
    console.log('💾 系统配置保存API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const configData = JSON.parse(body)
      const result = successResponse(configData, '保存成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  // 账号设置模块API
  // 管理员管理API
  'GET /account/admin/list': (req, res) => {
    console.log('👨‍💼 管理员列表API被调用')
    const adminList = Mock.mock({
      'list|15': [{
        'id|+1': 1,
        'username': '@word(5, 10)',
        'passwd_text': '******',
        'role_id|1-3': 1,
        'role_name': '@pick(["超级管理员", "管理员", "操作员"])',
        'status|0-1': 1,
        'last_login_time': '@datetime',
        'create_time': '@datetime',
        'remark': '@cparagraph(1, 2)'
      }],
      'total': 15
    })
    const result = successResponse(adminList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /account/admin/add': (req, res) => {
    console.log('➕ 新增管理员API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const adminData = JSON.parse(body)
      const result = successResponse(adminData, '新增管理员成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'PUT /account/admin/update': (req, res) => {
    console.log('✏️ 编辑管理员API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const adminData = JSON.parse(body)
      const result = successResponse(adminData, '编辑管理员成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'DELETE /account/admin/delete': (req, res) => {
    console.log('🗑️ 删除管理员API被调用')
    const result = successResponse(null, '删除管理员成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /account/admin/status': (req, res) => {
    console.log('🔄 管理员状态切换API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const statusData = JSON.parse(body)
      const result = successResponse(statusData, '状态更新成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  // 角色管理API
  'GET /account/role/list': (req, res) => {
    console.log('👥 角色列表API被调用')
    const roleList = Mock.mock({
      'list|10': [{
        'id|+1': 1,
        'role_name': '@pick(["超级管理员", "管理员", "操作员", "财务", "客服"])',
        'role_key': '@word(5, 10)',
        'permissions|3-8': ['@pick(["用户管理", "订单管理", "财务管理", "系统设置", "数据统计", "内容管理", "权限管理", "日志查看"])'],
        'rule_ids|3-8': [1, 2, 3, 4, 5, 6, 7, 8],
        'status|0-1': 1,
        'create_time': '@datetime',
        'remark': '@cparagraph(1, 2)'
      }],
      'total': 10
    })
    const result = successResponse(roleList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /account/role/add': (req, res) => {
    console.log('➕ 新增角色API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const roleData = JSON.parse(body)
      const result = successResponse(roleData, '新增角色成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'PUT /account/role/update': (req, res) => {
    console.log('✏️ 编辑角色API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const roleData = JSON.parse(body)
      const result = successResponse(roleData, '编辑角色成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'DELETE /account/role/delete': (req, res) => {
    console.log('🗑️ 删除角色API被调用')
    const result = successResponse(null, '删除角色成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /account/role/status': (req, res) => {
    console.log('🔄 角色状态切换API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const statusData = JSON.parse(body)
      const result = successResponse(statusData, '状态更新成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  // 菜单管理API
  'GET /account/menu/tree': (req, res) => {
    console.log('🌳 菜单树API被调用')
    const menuTree = Mock.mock([
      {
        'id': 1,
        'menu_name': '系统管理',
        'pid': 0,
        'icon': 'el-icon-setting',
        'route': '/system',
        'sort': 1,
        'is_show': 1,
        'is_menu': 1,
        'create_time': '@datetime',
        'children': [
          {
            'id': 11,
            'menu_name': '用户管理',
            'pid': 1,
            'icon': 'el-icon-user',
            'route': '/system/user',
            'sort': 1,
            'is_show': 1,
            'is_menu': 1,
            'create_time': '@datetime'
          },
          {
            'id': 12,
            'menu_name': '角色管理',
            'pid': 1,
            'icon': 'el-icon-s-custom',
            'route': '/system/role',
            'sort': 2,
            'is_show': 1,
            'is_menu': 1,
            'create_time': '@datetime'
          }
        ]
      },
      {
        'id': 2,
        'menu_name': '业务管理',
        'pid': 0,
        'icon': 'el-icon-s-order',
        'route': '/business',
        'sort': 2,
        'is_show': 1,
        'is_menu': 1,
        'create_time': '@datetime',
        'children': [
          {
            'id': 21,
            'menu_name': '订单管理',
            'pid': 2,
            'icon': 'el-icon-shopping-cart-2',
            'route': '/business/order',
            'sort': 1,
            'is_show': 1,
            'is_menu': 1,
            'create_time': '@datetime'
          }
        ]
      }
    ])
    const result = successResponse(menuTree)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /account/menu/add': (req, res) => {
    console.log('➕ 新增菜单API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const menuData = JSON.parse(body)
      const result = successResponse(menuData, '新增菜单成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'PUT /account/menu/update': (req, res) => {
    console.log('✏️ 编辑菜单API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const menuData = JSON.parse(body)
      const result = successResponse(menuData, '编辑菜单成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'DELETE /account/menu/delete': (req, res) => {
    console.log('🗑️ 删除菜单API被调用')
    const result = successResponse(null, '删除菜单成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 代理商管理API（新接口路径）
  'GET /api/admin/manage/agent/list': (req, res) => {
    console.log('🏢 代理商列表API被调用（新接口）')
    const url = new URL('http://localhost' + req.url)
    const pageNum = parseInt(url.searchParams.get('pageNum') || '1')
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10')
    const username = url.searchParams.get('username')
    const legalPersonName = url.searchParams.get('legalPersonName')
    const status = url.searchParams.get('status')
    const type = url.searchParams.get('type')

    const agentList = Mock.mock({
      'list|24': [{
        'id|+1': 75,
        'username': '@word(5, 10)',
        'passwd': '6C4C153C349B0C81AA1E4B7CC69C1217',
        'createTime': '@datetime("yyyy-MM-dd HH:mm:ss")',
        'isAdmin': 0,
        'isAgent|0-1': 1,
        'balance': 0,
        'userId|1-999': 24,
        'status|1': [-1, 0, 1, 2],
        'examinedText': '@cparagraph(0, 1)',
        'cash|0-100.2': 0,
        'legalPersonName': '@cname',
        'legalPersonIdcard': /^\d{17}[\dX]$/,
        'legalPersonTel': /^1[3-9]\d{9}$/,
        'legalPersonIdcardImg1': '@image("400x300", "#409eff", "#fff", "身份证正面")',
        'legalPersonIdcardImg2': '@image("400x300", "#409eff", "#fff", "身份证反面")',
        'legalPersonLicense': '@image("400x300", "#409eff", "#fff", "营业执照")',
        'type|1-3': 2,
        'cityId': '1046,1127,1131',
        'selectCityId|1046-1200': 1127,
        'roleId': 0
      }]
    })

    // 过滤数据
    let filteredList = agentList.list
    if (username) {
      filteredList = filteredList.filter(item =>
        item.username.toLowerCase().includes(username.toLowerCase())
      )
    }
    if (legalPersonName) {
      filteredList = filteredList.filter(item =>
        item.legalPersonName.includes(legalPersonName)
      )
    }
    if (status !== null && status !== '') {
      filteredList = filteredList.filter(item => item.status == status)
    }
    if (type !== null && type !== '') {
      filteredList = filteredList.filter(item => item.type == type)
    }

    // 分页处理
    const start = (pageNum - 1) * pageSize
    const end = start + pageSize
    const list = filteredList.slice(start, end)

    const result = successResponse({
      list,
      totalCount: filteredList.length,
      totalPage: Math.ceil(filteredList.length / pageSize),
      pageNum,
      pageSize
    })
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /api/admin/manage/agent/status': (req, res) => {
    console.log('🔄 代理商状态切换API被调用（新接口）')
    const result = successResponse(null, '状态修改成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /api/admin/manage/agent/examine': (req, res) => {
    console.log('✅ 代理商审核API被调用（新接口）')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const auditData = JSON.parse(body)
      const result = successResponse(auditData, '审核成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'POST /api/admin/manage/agent/add': (req, res) => {
    console.log('➕ 新增代理商API被调用（新接口）')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const agentData = JSON.parse(body)
      const result = successResponse(agentData, '新增代理商成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'POST /api/admin/manage/agent/edit': (req, res) => {
    console.log('✏️ 编辑代理商API被调用（新接口）')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const agentData = JSON.parse(body)
      const result = successResponse(agentData, '编辑代理商成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'POST /api/admin/manage/default/102': (req, res) => {
    console.log('🔑 修改代理商默认密码API被调用（新接口）')
    const result = successResponse(null, '默认密码修改成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 登录相关API（新接口路径）
  'POST /api/admin/login/logout': (req, res) => {
    console.log('🚪 退出登录API被调用（新接口）')
    const result = successResponse(null, '退出登录成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /api/admin/login/updatePass': (req, res) => {
    console.log('🔑 修改密码API被调用（新接口）')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      try {
        const passwordData = JSON.parse(body)
        console.log('🔑 修改密码请求数据:', {
          newPassword: '***已加密***',
          confirmPassword: '***已加密***'
        })

        // 验证密码是否一致
        if (passwordData.newPassword !== passwordData.confirmPassword) {
          const errorResult = {
            code: '400',
            msg: '两次输入的密码不一致',
            data: null
          }
          res.setHeader('Content-Type', 'application/json')
          res.end(JSON.stringify(errorResult))
          return
        }

        const result = successResponse(null, '密码修改成功')
        res.setHeader('Content-Type', 'application/json')
        res.end(JSON.stringify(result))
      } catch (error) {
        console.error('解析修改密码请求失败:', error)
        const errorResult = {
          code: '500',
          msg: '请求数据格式错误',
          data: null
        }
        res.setHeader('Content-Type', 'application/json')
        res.end(JSON.stringify(errorResult))
      }
    })
  },

  'GET /api/admin/manage/tree': (req, res) => {
    console.log('🌳 获取代理城市树API被调用（新接口）')

    // 模拟城市树数据
    const cityTreeData = [
      {
        "id": 1,
        "trueName": "北京",
        "pid": 0,
        "children": [
          {
            "id": 2,
            "trueName": "北京市",
            "pid": 1,
            "children": [
              { "id": 3, "trueName": "东城区", "pid": 2 },
              { "id": 4, "trueName": "西城区", "pid": 2 },
              { "id": 5, "trueName": "朝阳区", "pid": 2 },
              { "id": 6, "trueName": "丰台区", "pid": 2 },
              { "id": 7, "trueName": "石景山区", "pid": 2 },
              { "id": 8, "trueName": "海淀区", "pid": 2 },
              { "id": 9, "trueName": "门头沟区", "pid": 2 },
              { "id": 10, "trueName": "房山区", "pid": 2 },
              { "id": 11, "trueName": "通州区", "pid": 2 },
              { "id": 12, "trueName": "顺义区", "pid": 2 },
              { "id": 13, "trueName": "昌平区", "pid": 2 },
              { "id": 14, "trueName": "大兴区", "pid": 2 },
              { "id": 15, "trueName": "怀柔区", "pid": 2 },
              { "id": 16, "trueName": "平谷区", "pid": 2 },
              { "id": 17, "trueName": "密云县", "pid": 2 },
              { "id": 18, "trueName": "延庆县", "pid": 2 }
            ]
          }
        ]
      },
      {
        "id": 19,
        "trueName": "天津",
        "pid": 0,
        "children": [
          {
            "id": 20,
            "trueName": "天津市",
            "pid": 19,
            "children": [
              { "id": 21, "trueName": "和平区", "pid": 20 },
              { "id": 22, "trueName": "河东区", "pid": 20 },
              { "id": 23, "trueName": "河西区", "pid": 20 },
              { "id": 24, "trueName": "南开区", "pid": 20 },
              { "id": 25, "trueName": "河北区", "pid": 20 },
              { "id": 26, "trueName": "红桥区", "pid": 20 },
              { "id": 27, "trueName": "东丽区", "pid": 20 },
              { "id": 28, "trueName": "西青区", "pid": 20 },
              { "id": 29, "trueName": "津南区", "pid": 20 },
              { "id": 30, "trueName": "北辰区", "pid": 20 },
              { "id": 31, "trueName": "武清区", "pid": 20 },
              { "id": 32, "trueName": "宝坻区", "pid": 20 },
              { "id": 33, "trueName": "滨海新区", "pid": 20 },
              { "id": 34, "trueName": "宁河县", "pid": 20 },
              { "id": 35, "trueName": "静海县", "pid": 20 },
              { "id": 36, "trueName": "蓟县", "pid": 20 }
            ]
          }
        ]
      },
      {
        "id": 37,
        "trueName": "河北省",
        "pid": 0,
        "children": [
          {
            "id": 38,
            "trueName": "石家庄市",
            "pid": 37,
            "children": [
              { "id": 39, "trueName": "长安区", "pid": 38 },
              { "id": 40, "trueName": "桥西区", "pid": 38 },
              { "id": 41, "trueName": "新华区", "pid": 38 },
              { "id": 42, "trueName": "井陉矿区", "pid": 38 },
              { "id": 43, "trueName": "裕华区", "pid": 38 }
            ]
          },
          {
            "id": 61,
            "trueName": "唐山市",
            "pid": 37,
            "children": [
              { "id": 62, "trueName": "路南区", "pid": 61 },
              { "id": 63, "trueName": "路北区", "pid": 61 },
              { "id": 64, "trueName": "古冶区", "pid": 61 },
              { "id": 65, "trueName": "开平区", "pid": 61 }
            ]
          }
        ]
      }
    ]

    const result = successResponse(cityTreeData)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 代理商管理API（旧接口，保持兼容）
  'GET /account/franchisee/list': (req, res) => {
    console.log('🏢 代理商列表API被调用')
    const franchiseeList = Mock.mock({
      'list|15': [{
        'id|+1': 1,
        'user_id|1000-9999': 1000,
        'username': '@cname',
        'legal_person_name': '@cname',
        'legal_person_tel': /^1[3-9]\d{9}$/,
        'legal_person_idcard': /^\d{17}[\dX]$/,
        'type|1-3': 1,
        'city_name': '@city',
        'status|0-2': 1,
        'create_time': '@datetime',
        'remark': '@cparagraph(1, 2)'
      }],
      'total': 15
    })
    const result = successResponse(franchiseeList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /account/franchisee/add': (req, res) => {
    console.log('➕ 新增代理商API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const franchiseeData = JSON.parse(body)
      const result = successResponse(franchiseeData, '新增代理商成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'PUT /account/franchisee/update': (req, res) => {
    console.log('✏️ 编辑代理商API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const franchiseeData = JSON.parse(body)
      const result = successResponse(franchiseeData, '编辑代理商成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'DELETE /account/franchisee/delete': (req, res) => {
    console.log('🗑️ 删除代理商API被调用')
    const result = successResponse(null, '删除代理商成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /account/franchisee/audit': (req, res) => {
    console.log('📋 代理商审核API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const auditData = JSON.parse(body)
      const result = successResponse(auditData, '审核成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'GET /account/franchisee/config': (req, res) => {
    console.log('⚙️ 代理商配置获取API被调用')
    const config = Mock.mock({
      'agent_area': '@integer(5, 15)',
      'agent_city': '@integer(3, 10)',
      'agent_privince': '@integer(1, 5)'
    })
    const result = successResponse(config)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /account/franchisee/config': (req, res) => {
    console.log('💾 代理商配置保存API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const configData = JSON.parse(body)
      const result = successResponse(configData, '配置保存成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  // 第三方管理API
  'GET /account/third/list': (req, res) => {
    console.log('🔗 第三方列表API被调用')
    const thirdList = Mock.mock({
      'list|10': [{
        'id|+1': 1,
        'name': '@pick(["阿里云服务", "腾讯云服务", "百度AI", "微信支付", "支付宝", "顺丰快递", "京东物流"])',
        'tel': /^1[3-9]\d{9}$/,
        'province': '@integer(1, 34)',
        'city': '@integer(1, 100)',
        'city_name': '@city',
        'address': '@county(true)',
        'service_type': ['@pick(["维修服务", "清洁服务", "搬家服务", "装修服务"])'],
        'api_key': '@string("lower", 32)',
        'status|0-1': 1,
        'create_time': '@datetime',
        'remark': '@cparagraph(1, 2)'
      }],
      'total': 10
    })
    const result = successResponse(thirdList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /account/third/add': (req, res) => {
    console.log('➕ 新增第三方API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const thirdData = JSON.parse(body)
      const result = successResponse(thirdData, '新增第三方成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'PUT /account/third/update': (req, res) => {
    console.log('✏️ 编辑第三方API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const thirdData = JSON.parse(body)
      const result = successResponse(thirdData, '编辑第三方成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'DELETE /account/third/delete': (req, res) => {
    console.log('🗑️ 删除第三方API被调用')
    const result = successResponse(null, '删除第三方成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /account/third/status': (req, res) => {
    console.log('🔄 第三方状态切换API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const statusData = JSON.parse(body)
      const result = successResponse(statusData, '状态更新成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'GET /account/third/config': (req, res) => {
    console.log('⚙️ 第三方配置获取API被调用')
    const config = Mock.mock({
      'api_url': '@url',
      'api_key': '@string("lower", 32)',
      'api_secret': '@string("lower", 64)',
      'timeout': '@integer(10, 60)',
      'retry_count': '@integer(1, 5)',
      'enabled|0-1': 1
    })
    const result = successResponse(config)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /account/third/config': (req, res) => {
    console.log('💾 第三方配置保存API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const configData = JSON.parse(body)
      const result = successResponse(configData, '配置保存成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'POST /account/third/test-connection': (req, res) => {
    console.log('🔍 第三方连接测试API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const testData = JSON.parse(body)
      const result = successResponse(testData, '连接测试成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  // 系统设置扩展API
  // 微信审核管理API
  'GET /system/examine/list': (req, res) => {
    console.log('📱 微信审核列表API被调用')
    const examineList = Mock.mock({
      'list|10': [{
        'id|+1': 1,
        'version': 'v@integer(1, 9).@integer(0, 9).@integer(0, 9)',
        'content': '@cparagraph(2, 4)',
        'status|1-3': 1,
        'submit_time': '@datetime',
        'audit_time': function() {
          return this.status > 1 ? '@datetime' : ''
        },
        'audit_reason': function() {
          return this.status === 3 ? '@cparagraph(1, 2)' : ''
        }
      }],
      'total': 10
    })
    const result = successResponse(examineList)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /system/examine/submit': (req, res) => {
    console.log('📤 微信审核提交API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const examineData = JSON.parse(body)
      const result = successResponse(examineData, '提交审核成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'GET /system/examine/detail': (req, res) => {
    console.log('📋 微信审核详情API被调用')
    const examineDetail = Mock.mock({
      'id': '@integer(1, 100)',
      'version': 'v@integer(1, 9).@integer(0, 9).@integer(0, 9)',
      'content': '@cparagraph(2, 4)',
      'status|1-3': 1,
      'submit_time': '@datetime',
      'audit_time': '@datetime',
      'audit_reason': '@cparagraph(1, 2)'
    })
    const result = successResponse(examineDetail)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /system/examine/resubmit': (req, res) => {
    console.log('🔄 微信审核重新提交API被调用')
    const result = successResponse(null, '重新提交成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 版本管理API
  'GET /system/version/history': (req, res) => {
    console.log('📚 版本历史API被调用')
    const versionHistory = Mock.mock({
      'list|15': [{
        'id|+1': 1,
        'version': 'v@integer(1, 9).@integer(0, 9).@integer(0, 9)',
        'type': '@pick(["stable", "beta", "alpha"])',
        'size': '@integer(10, 100).@integer(0, 9)MB',
        'description': '@cparagraph(2, 4)',
        'release_time': '@datetime',
        'is_current|0-1': 0
      }],
      'total': 15
    })
    // 设置第一个为当前版本
    if (versionHistory.list.length > 0) {
      versionHistory.list[0].is_current = 1
    }
    const result = successResponse(versionHistory)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'GET /system/version/check-update': (req, res) => {
    console.log('🔍 检查更新API被调用')
    const updateInfo = Mock.mock({
      'has_update|0-1': 1,
      'latest_version': {
        'version': 'v@integer(1, 9).@integer(0, 9).@integer(0, 9)',
        'type': 'stable',
        'size': '@integer(10, 100).@integer(0, 9)MB',
        'description': '@cparagraph(2, 4)',
        'release_time': '@datetime'
      }
    })
    const result = successResponse(updateInfo)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'GET /system/version/detail': (req, res) => {
    console.log('📋 版本详情API被调用')
    const versionDetail = Mock.mock({
      'version': 'v@integer(1, 9).@integer(0, 9).@integer(0, 9)',
      'type': '@pick(["stable", "beta", "alpha"])',
      'size': '@integer(10, 100).@integer(0, 9)MB',
      'release_time': '@datetime',
      'changelog': '<h3>更新内容</h3><ul><li>新增用户管理功能</li><li>修复已知问题</li><li>优化系统性能</li></ul>'
    })
    const result = successResponse(versionDetail)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /system/version/rollback': (req, res) => {
    console.log('⏪ 版本回滚API被调用')
    const result = successResponse(null, '版本回滚成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 通用API
  'GET /common/province': (req, res) => {
    console.log('🗺️ 省份列表API被调用')
    const provinceList = Mock.mock({
      'list|34': [{
        'id|+1': 1,
        'title': '@province'
      }]
    })
    const result = successResponse(provinceList.list)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'GET /common/city': (req, res) => {
    console.log('🏙️ 城市列表API被调用')
    const cityList = Mock.mock({
      'list|20': [{
        'id|+1': 1,
        'title': '@city'
      }]
    })
    const result = successResponse(cityList.list)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 系统设置配置API
  'GET /system/wechat/config': (req, res) => {
    console.log('📱 小程序配置获取API被调用')
    const config = Mock.mock({
      'app_name': '家政服务小程序',
      'app_id': '@string("lower", 18)',
      'app_secret': '@string("lower", 32)',
      'mch_id': '@string("number", 10)',
      'mch_key': '@string("lower", 32)',
      'version': 'v@integer(1, 9).@integer(0, 9).@integer(0, 9)',
      'status|0-1': 1,
      'debug|0-1': 0,
      'remark': '@cparagraph(1, 2)'
    })
    const result = successResponse(config)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /system/wechat/config': (req, res) => {
    console.log('💾 小程序配置保存API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const configData = JSON.parse(body)
      const result = successResponse(configData, '配置保存成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'POST /system/wechat/test': (req, res) => {
    console.log('🔍 小程序连接测试API被调用')
    const result = successResponse(null, '连接测试成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'GET /system/upgrade/check-update': (req, res) => {
    console.log('🔍 系统升级检查更新API被调用')
    const updateInfo = Mock.mock({
      'has_update|0-1': 1,
      'latest_version': {
        'version': 'v@integer(3, 9).@integer(0, 9).@integer(0, 9)',
        'type': 'stable',
        'size': '@integer(10, 100).@integer(0, 9)MB',
        'description': '@cparagraph(2, 4)',
        'release_time': '@datetime'
      }
    })
    const result = successResponse(updateInfo)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 其他系统配置API
  'GET /system/web/config': (req, res) => {
    console.log('🌐 公众号配置获取API被调用')
    const config = Mock.mock({
      'name': '家政服务公众号',
      'app_id': '@string("lower", 18)',
      'app_secret': '@string("lower", 32)',
      'token': '@string("lower", 32)',
      'status|0-1': 1
    })
    const result = successResponse(config)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /system/web/config': (req, res) => {
    console.log('💾 公众号配置保存API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const configData = JSON.parse(body)
      const result = successResponse(configData, '配置保存成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  // 批量创建其他系统配置API
  'GET /system/app/config': (req, res) => {
    console.log('📱 APP配置获取API被调用')
    const config = Mock.mock({
      'app_name': '家政服务APP',
      'version': 'v@integer(1, 9).@integer(0, 9).@integer(0, 9)',
      'download_url': '@url',
      'update_desc': '@cparagraph(2, 3)',
      'force_update|0-1': 0
    })
    const result = successResponse(config)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /system/app/config': (req, res) => {
    console.log('💾 APP配置保存API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const configData = JSON.parse(body)
      const result = successResponse(configData, '配置保存成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'GET /system/payment/config': (req, res) => {
    console.log('💳 支付配置获取API被调用')
    const config = Mock.mock({
      'wechat': {
        'mch_id': '@string("number", 10)',
        'mch_key': '@string("lower", 32)',
        'cert_path': '/path/to/cert.pem',
        'status|0-1': 1
      },
      'alipay': {
        'app_id': '@string("number", 16)',
        'private_key': '@string("lower", 128)',
        'public_key': '@string("lower", 128)',
        'status|0-1': 1
      }
    })
    const result = successResponse(config)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /system/payment/wechat': (req, res) => {
    console.log('💾 微信支付配置保存API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const configData = JSON.parse(body)
      const result = successResponse(configData, '微信支付配置保存成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'PUT /system/payment/alipay': (req, res) => {
    console.log('💾 支付宝配置保存API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const configData = JSON.parse(body)
      const result = successResponse(configData, '支付宝配置保存成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'POST /system/payment/wechat/test': (req, res) => {
    console.log('🔍 微信支付连接测试API被调用')
    const result = successResponse(null, '微信支付连接测试成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /system/payment/alipay/test': (req, res) => {
    console.log('🔍 支付宝连接测试API被调用')
    const result = successResponse(null, '支付宝连接测试成功')
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 其他系统配置API的通用模式
  'GET /system/info/config': (req, res) => {
    console.log('📄 隐私协议配置获取API被调用')
    const config = Mock.mock({
      'user_agreement': '@cparagraph(5, 10)',
      'privacy_policy': '@cparagraph(5, 10)'
    })
    const result = successResponse(config)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /system/info/config': (req, res) => {
    console.log('💾 隐私协议配置保存API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const configData = JSON.parse(body)
      const result = successResponse(configData, '配置保存成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'GET /system/upload/config': (req, res) => {
    console.log('📤 上传配置获取API被调用')
    const config = Mock.mock({
      'upload_type': '@pick(["local", "oss", "qiniu"])',
      'max_size': '@integer(1, 100)',
      'allowed_types': 'jpg,png,gif,pdf'
    })
    const result = successResponse(config)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /system/upload/config': (req, res) => {
    console.log('💾 上传配置保存API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const configData = JSON.parse(body)
      const result = successResponse(configData, '配置保存成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  'GET /system/transaction/config': (req, res) => {
    console.log('💰 交易配置获取API被调用')
    const config = Mock.mock({
      'transaction_fee': '@float(0, 5, 2, 2)',
      'min_amount': '@float(0, 100, 2, 2)',
      'max_amount': '@float(1000, 50000, 2, 2)'
    })
    const result = successResponse(config)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'PUT /system/transaction/config': (req, res) => {
    console.log('💾 交易配置保存API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const configData = JSON.parse(body)
      const result = successResponse(configData, '配置保存成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  // ==================== 隐私协议配置API ====================

  'GET /api/admin/config/getPrivacyAgreement': (req, res) => {
    console.log('📄 获取隐私协议配置API被调用')
    const config = {
      loginProtocol: '<p><span style="font-size: 20px;"><strong>用户隐私协议</strong></span></p><p><br/></p><p>本政策仅适用于安徽祚晟电子科技有限公司的今师傅产品或服务。</p><p><br/></p><p>最近更新日期：2024-1-9</p><p><br/></p><p>应用名称：今师傅</p><p><br/></p><p>开发者名称：安徽祚晟电子科技有限公司</p>',
      informationProtection: '<p><span style="font-size: 20px;"><strong>个人信息保护指引</strong></span></p><p><br/></p><p>本公司设立了用户信息保护专职部门。</p><p><br/></p><p>联系电话：18255871118</p><p><br/></p><p>一般情况下，本公司将在十五个工作日内回复。</p>',
      content: '<p><span style="font-size: 20px;"><strong>师傅端隐私协议</strong></span></p><p><br/></p><p>本政策仅适用于安徽祚晟电子科技有限公司的今师傅师傅端产品或服务。</p><p><br/></p><p>最近更新日期：2024-1-9</p><p><br/></p><p>应用名称：今师傅（师傅端）</p>',
      shifuServiceAgreement: '<p><span style="font-size: 18px; color: rgb(51, 51, 51);"><strong>今师傅师傅端服务协议</strong></span></p><p><br/></p><p><span style="color: rgb(51, 51, 51);">欢迎使用今师傅师傅端服务！</span></p><p><br/></p><p><span style="color: rgb(51, 51, 51);">本协议是您与安徽祚晟电子科技有限公司之间关于您使用今师傅师傅端服务所订立的协议。</span></p>',
      shifuSecurityProtocol: '<p><span style="font-size: 18px;"><strong>今师傅师傅端安全协议</strong></span></p><p><br/></p><p>协议更新日期：2024-1-9</p><p><br/></p><p>为了保障师傅和用户的权益，维护平台的正常运营秩序，特制定本安全协议。</p>',
      shifuQualityCommitment: '<p><span style="font-size: 18px; color: rgb(51, 51, 51);"><strong>服务质量保障承诺</strong></span></p><p><br/></p><p><span style="color: rgb(51, 51, 51);">我们承诺为用户提供优质的服务体验，确保服务质量符合平台标准。</span></p><p><br/></p><p><span style="color: rgb(51, 51, 51);">1. 服务态度：师傅应保持良好的服务态度，耐心解答用户问题。</span></p>',
      userNotice: '<p><span style="font-size: 20px; color: rgb(255, 0, 0);">用户须知</span></p><p><br/></p><p><span style="color: rgb(255, 0, 0); font-size: 12px;">1. 用户在使用平台服务时，应遵守相关法律法规。</span></p><p><br/></p><p><span style="color: rgb(255, 0, 0); font-size: 12px;">2. 用户应如实填写个人信息，确保信息的真实性和准确性。</span></p>',
      masterNotice: '<p><span style="font-size: 18px; color: rgb(51, 51, 51);"><strong>师傅须知</strong></span></p><p><br/></p><p><span style="color: rgb(51, 51, 51);">1. 师傅应具备相应的专业技能和资质，确保能够提供优质的服务。</span></p><p><br/></p><p><span style="color: rgb(51, 51, 51);">2. 师傅应遵守平台的各项规定，维护平台的良好秩序。</span></p>'
    }
    const result = successResponse(config)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /api/admin/config/updatePrivacyAgreement': (req, res) => {
    console.log('💾 更新隐私协议配置API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const configData = JSON.parse(body)
      console.log('📝 接收到的隐私协议数据:', configData)
      const result = successResponse(configData, '隐私协议配置更新成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  // ==================== 交易设置配置API ====================

  'GET /api/admin/config/getTradeSettings': (req, res) => {
    console.log('💰 获取交易设置配置API被调用')
    const config = {
      companyPay: 2,
      overTime: 1440,
      maxDay: 7,
      timeUnit: 30,
      tradingRules: '<p><span style="font-size: 18px; color: rgb(51, 51, 51);"><strong>账户交易规则</strong></span></p><p><br/></p><p><span style="color: rgb(51, 51, 51);">1. 所有交易必须通过平台进行，禁止私下交易。</span></p><p><br/></p><p><span style="color: rgb(51, 51, 51);">2. 订单完成后，系统将自动结算费用。</span></p><p><br/></p><p><span style="color: rgb(51, 51, 51);">3. 如遇纠纷，请及时联系客服处理。</span></p><p><br/></p><p><span style="color: rgb(51, 51, 51);">4. 平台保留对违规行为的最终解释权。</span></p>'
    }
    const result = successResponse(config)
    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  'POST /api/admin/config/updateTradeSettings': (req, res) => {
    console.log('💾 更新交易设置配置API被调用')
    let body = ''
    req.on('data', chunk => { body += chunk })
    req.on('end', () => {
      const configData = JSON.parse(body)
      console.log('📝 接收到的交易设置数据:', configData)
      const result = successResponse(configData, '交易设置配置更新成功')
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(result))
    })
  },

  // ==================== 文件上传接口 ====================

  'POST /api/admin/file/upload': (req, res) => {
    console.log('📤 核心文件上传API被调用')

    // 模拟文件上传成功
    const mockImageUrl = Mock.mock('@image("800x600", "#4A90E2", "#FFF", "png", "Mock Image")')

    const result = successResponse({
      url: mockImageUrl,
      fileUrl: mockImageUrl,
      file_url: mockImageUrl,
      fileName: 'mock-image-' + Date.now() + '.png',
      fileSize: Mock.mock('@integer(10000, 500000)'),
      fileType: 'image/png'
    }, '文件上传成功')

    res.setHeader('Content-Type', 'application/json')
    res.end(JSON.stringify(result))
  },

  // 批量添加其他系统配置API
  'GET /system/notice/config': createConfigAPI('通知'),
  'PUT /system/notice/config': createSaveAPI('通知'),
  'GET /system/message/config': createConfigAPI('短信'),
  'PUT /system/message/config': createSaveAPI('短信'),
  'GET /system/information/config': createConfigAPI('备案信息'),
  'PUT /system/information/config': createSaveAPI('备案信息'),
  'GET /system/print/config': createConfigAPI('打印机'),
  'PUT /system/print/config': createSaveAPI('打印机'),
  'POST /system/print/test': createTestAPI('打印机'),
  'GET /system/car-fee/config': createConfigAPI('车费'),
  'PUT /system/car-fee/config': createSaveAPI('车费'),
  'GET /system/city/config': createConfigAPI('城市'),
  'PUT /system/city/config': createSaveAPI('城市'),
  'GET /system/travel/config': createConfigAPI('出行'),
  'PUT /system/travel/config': createSaveAPI('出行'),
  'GET /system/other/config': createConfigAPI('其他'),
  'PUT /system/other/config': createSaveAPI('其他')
}

// Vite插件
export default function viteMockPlugin() {
  return {
    name: 'vite-mock-plugin',
    configureServer(server) {
      console.log('🎯 Vite Mock插件开始加载...')

      server.middlewares.use('/api', (req, res, next) => {
        const method = req.method
        const url = req.url
        const routeKey = `${method} ${url.split('?')[0]}`

        console.log(`🔍 API请求: ${method} ${url}`)

        // 查找匹配的路由
        let handler = mockRoutes[routeKey]

        // 如果没有找到精确匹配，尝试模式匹配
        if (!handler) {
          // 处理详情API的动态路由
          if (method === 'GET' && url.match(/^\/service\/detail\/\d+/)) {
            handler = mockRoutes['GET /service/detail']
          }
          // 处理更新API的动态路由
          else if (method === 'PUT' && url.match(/^\/service\/update\/\d+/)) {
            handler = mockRoutes['PUT /service/update']
          }
          // 处理金刚区删除API的动态路由
          else if (method === 'DELETE' && url.match(/^\/service\/jingang\/delete\/\d+/)) {
            handler = mockRoutes['DELETE /service/jingang/delete']
          }
          // 处理服务点删除API的动态路由
          else if (method === 'DELETE' && url.match(/^\/service\/daili\/delete\/\d+/)) {
            handler = mockRoutes['DELETE /service/daili/delete']
          }
          // 处理服务点状态切换API的动态路由
          else if (method === 'PUT' && url.match(/^\/service\/daili\/switch\/\d+/)) {
            handler = mockRoutes['PUT /service/daili/switch']
          }
          // 处理项目配置删除API的动态路由
          else if (method === 'DELETE' && url.match(/^\/service\/peizhi\/delete\/\d+/)) {
            handler = mockRoutes['DELETE /service/peizhi/delete']
          }
          // 处理一口价相关API的动态路由
          else if (method === 'GET' && url.match(/^\/admin\/oneprice\/list/)) {
            handler = mockRoutes['GET /admin/oneprice/list']
          }
          else if (method === 'GET' && url.match(/^\/admin\/oneprice\/info\/\d+/)) {
            handler = mockRoutes['GET /admin/oneprice/info']
          }
          else if (method === 'POST' && url.match(/^\/admin\/oneprice\/add/)) {
            handler = mockRoutes['POST /admin/oneprice/add']
          }
          else if (method === 'POST' && url.match(/^\/admin\/oneprice\/update/)) {
            handler = mockRoutes['POST /admin/oneprice/update']
          }
          else if (method === 'POST' && url.match(/^\/admin\/oneprice\/delete\/\d+/)) {
            handler = mockRoutes['POST /admin/oneprice/delete']
          }
          else if (method === 'POST' && url.match(/^\/admin\/oneprice\/toggleStatus/)) {
            handler = mockRoutes['POST /admin/oneprice/toggleStatus']
          }
          else if (method === 'GET' && url.match(/^\/admin\/oneprice\/priceSetting\/list/)) {
            handler = mockRoutes['GET /admin/oneprice/priceSetting/list']
          }
          // 处理师傅详情API的动态路由
          else if (method === 'GET' && url.match(/^\/technician\/detail\/\d+/)) {
            handler = mockRoutes['GET /technician/detail']
          }
          // 处理师傅更新API的动态路由
          else if (method === 'PUT' && url.match(/^\/technician\/update\/\d+/)) {
            handler = mockRoutes['PUT /technician/update']
          }
          // 处理师傅状态切换API的动态路由
          else if (method === 'PUT' && url.match(/^\/technician\/toggle\/\d+/)) {
            handler = mockRoutes['PUT /technician/toggle']
          }
          // 处理师傅删除API的动态路由
          else if (method === 'DELETE' && url.match(/^\/technician\/delete\/\d+/)) {
            handler = mockRoutes['DELETE /technician/delete']
          }
          // 处理师傅等级更新API的动态路由
          else if (method === 'PUT' && url.match(/^\/technician\/level\/update\/\d+/)) {
            handler = mockRoutes['PUT /technician/level/update']
          }
          // 处理师傅等级删除API的动态路由
          else if (method === 'DELETE' && url.match(/^\/technician\/level\/delete\/\d+/)) {
            handler = mockRoutes['DELETE /technician/level/delete']
          }
          // 处理师傅押金退还API的动态路由
          else if (method === 'PUT' && url.match(/^\/technician\/deposit\/refund\/\d+/)) {
            handler = mockRoutes['PUT /technician/deposit/refund']
          }
          // 处理师傅服务区域更新API的动态路由
          else if (method === 'PUT' && url.match(/^\/technician\/area\/update\/\d+/)) {
            handler = mockRoutes['PUT /technician/area/update']
          }
          // 处理师傅服务区域状态切换API的动态路由
          else if (method === 'PUT' && url.match(/^\/technician\/area\/toggle\/\d+/)) {
            handler = mockRoutes['PUT /technician/area/toggle']
          }
          // 处理师傅服务区域删除API的动态路由
          else if (method === 'DELETE' && url.match(/^\/technician\/area\/delete\/\d+/)) {
            handler = mockRoutes['DELETE /technician/area/delete']
          }
          // 处理卡券详情API的动态路由
          else if (method === 'GET' && url.match(/^\/api\/admin\/coupon\/detail\/\d+/)) {
            handler = mockRoutes['GET /api/admin/coupon/detail']
          }
          // 处理卡券更新API的动态路由
          else if (method === 'POST' && url.match(/^\/api\/admin\/coupon\/edit/)) {
            handler = mockRoutes['POST /api/admin/coupon/edit']
          }
          // 处理卡券状态切换API的动态路由
          else if (method === 'POST' && url.match(/^\/api\/admin\/coupon\/status\/\d+/)) {
            handler = mockRoutes['POST /api/admin/coupon/status']
          }
          // 处理卡券删除API的动态路由
          else if (method === 'POST' && url.match(/^\/api\/admin\/coupon\/delete\/\d+/)) {
            handler = mockRoutes['POST /api/admin/coupon/delete']
          }
          // 处理通知更新API的动态路由
          else if (method === 'PUT' && url.match(/^\/market\/notice\/update\/\d+/)) {
            handler = mockRoutes['PUT /market/notice/update']
          }
          // 处理通知删除API的动态路由
          else if (method === 'DELETE' && url.match(/^\/market\/notice\/delete\/\d+/)) {
            handler = mockRoutes['DELETE /market/notice/delete']
          }
          // 处理订单详情API的动态路由
          else if (method === 'GET' && url.match(/^\/shop\/order\/detail\/\d+/)) {
            handler = mockRoutes['GET /shop/order/detail']
          }
          // 处理订单取消API的动态路由
          else if (method === 'PUT' && url.match(/^\/shop\/order\/cancel\/\d+/)) {
            handler = mockRoutes['PUT /shop/order/cancel']
          }
          // 处理订单完成API的动态路由
          else if (method === 'PUT' && url.match(/^\/shop\/order\/complete\/\d+/)) {
            handler = mockRoutes['PUT /shop/order/complete']
          }
          // 处理退款详情API的动态路由
          else if (method === 'GET' && url.match(/^\/shop\/refund\/detail\/\d+/)) {
            handler = mockRoutes['GET /shop/refund/detail']
          }
          // 处理退款同意API的动态路由
          else if (method === 'PUT' && url.match(/^\/shop\/refund\/approve\/\d+/)) {
            handler = mockRoutes['PUT /shop/refund/approve']
          }
          // 处理退款拒绝API的动态路由
          else if (method === 'PUT' && url.match(/^\/shop\/refund\/reject\/\d+/)) {
            handler = mockRoutes['PUT /shop/refund/reject']
          }
          // 处理分销商详情API的动态路由
          else if (method === 'GET' && url.match(/^\/distribution\/examine\/detail\/\d+/)) {
            handler = mockRoutes['GET /distribution/examine/detail']
          }
          // 处理分销审核通过API的动态路由
          else if (method === 'PUT' && url.match(/^\/distribution\/examine\/approve\/\d+/)) {
            handler = mockRoutes['PUT /distribution/examine/approve']
          }
          // 处理分销审核拒绝API的动态路由
          else if (method === 'PUT' && url.match(/^\/distribution\/examine\/reject\/\d+/)) {
            handler = mockRoutes['PUT /distribution/examine/reject']
          }
          // 处理分销审核API的动态路由
          else if (method === 'PUT' && url.match(/^\/distribution\/examine\/audit\/\d+/)) {
            handler = mockRoutes['PUT /distribution/examine/audit']
          }
          // 处理分销商详情API的动态路由
          else if (method === 'GET' && url.match(/^\/distribution\/detail\/\d+/)) {
            handler = mockRoutes['GET /distribution/detail']
          }
          // 处理分销商状态切换API的动态路由
          else if (method === 'PUT' && url.match(/^\/distribution\/status\/\d+/)) {
            handler = mockRoutes['PUT /distribution/status']
          }

          // 处理储值详情API的动态路由
          else if (method === 'GET' && url.match(/^\/finance\/stored\/detail\/\d+/)) {
            handler = mockRoutes['GET /finance/stored/detail']
          }
          // 处理余额调整API的动态路由
          else if (method === 'PUT' && url.match(/^\/finance\/stored\/adjust\/\d+/)) {
            handler = mockRoutes['PUT /finance/stored/adjust']
          }
          // 处理用户详情API的动态路由
          else if (method === 'GET' && url.match(/^\/user\/detail\/\d+/)) {
            handler = mockRoutes['GET /user/detail']
          }
          // 处理用户客服设置API的动态路由
          else if (method === 'PUT' && url.match(/^\/user\/customer-service\/\d+/)) {
            handler = mockRoutes['PUT /user/customer-service']
          }
          // 处理用户状态切换API的动态路由
          else if (method === 'PUT' && url.match(/^\/user\/status\/\d+/)) {
            handler = mockRoutes['PUT /user/status']
          }
          // 处理评价详情API的动态路由
          else if (method === 'GET' && url.match(/^\/shop\/evaluate\/detail\/\d+/)) {
            handler = mockRoutes['GET /shop/evaluate/detail']
          }
          // 处理评价回复API的动态路由
          else if (method === 'PUT' && url.match(/^\/shop\/evaluate\/reply\/\d+/)) {
            handler = mockRoutes['PUT /shop/evaluate/reply']
          }
          // 处理评价删除API的动态路由
          else if (method === 'DELETE' && url.match(/^\/shop\/evaluate\/delete\/\d+/)) {
            handler = mockRoutes['DELETE /shop/evaluate/delete']
          }
          // 处理佣金结算API的动态路由
          else if (method === 'PUT' && url.match(/^\/shop\/commission\/settle\/\d+/)) {
            handler = mockRoutes['PUT /shop/commission/settle']
          }
          // 处理售后详情API的动态路由
          else if (method === 'GET' && url.match(/^\/shop\/aftersale\/detail\/\d+/)) {
            handler = mockRoutes['GET /shop/aftersale/detail']
          }
          // 处理售后处理API的动态路由
          else if (method === 'PUT' && url.match(/^\/shop\/aftersale\/handle\/\d+/)) {
            handler = mockRoutes['PUT /shop/aftersale/handle']
          }
          // 处理售后删除API的动态路由
          else if (method === 'DELETE' && url.match(/^\/shop\/aftersale\/delete\/\d+/)) {
            handler = mockRoutes['DELETE /shop/aftersale/delete']
          }
          // 处理管理员更新API的动态路由
          else if (method === 'PUT' && url.match(/^\/account\/admin\/update\/\d+/)) {
            handler = mockRoutes['PUT /account/admin/update']
          }
          // 处理管理员删除API的动态路由
          else if (method === 'DELETE' && url.match(/^\/account\/admin\/delete\/\d+/)) {
            handler = mockRoutes['DELETE /account/admin/delete']
          }
          // 处理管理员状态切换API的动态路由
          else if (method === 'PUT' && url.match(/^\/account\/admin\/status\/\d+/)) {
            handler = mockRoutes['PUT /account/admin/status']
          }
          // 处理角色更新API的动态路由
          else if (method === 'PUT' && url.match(/^\/account\/role\/update\/\d+/)) {
            handler = mockRoutes['PUT /account/role/update']
          }
          // 处理角色删除API的动态路由
          else if (method === 'DELETE' && url.match(/^\/account\/role\/delete\/\d+/)) {
            handler = mockRoutes['DELETE /account/role/delete']
          }
          // 处理角色状态切换API的动态路由
          else if (method === 'PUT' && url.match(/^\/account\/role\/status\/\d+/)) {
            handler = mockRoutes['PUT /account/role/status']
          }
          // 处理菜单更新API的动态路由
          else if (method === 'PUT' && url.match(/^\/account\/menu\/update\/\d+/)) {
            handler = mockRoutes['PUT /account/menu/update']
          }
          // 处理菜单删除API的动态路由
          else if (method === 'DELETE' && url.match(/^\/account\/menu\/delete\/\d+/)) {
            handler = mockRoutes['DELETE /account/menu/delete']
          }
          // 处理代理商状态切换API的动态路由（新接口）
          else if (method === 'POST' && url.match(/^\/api\/admin\/manage\/agent\/status\/\d+/)) {
            handler = mockRoutes['POST /api/admin/manage/agent/status']
          }
          // 处理代理商更新API的动态路由
          else if (method === 'PUT' && url.match(/^\/account\/franchisee\/update\/\d+/)) {
            handler = mockRoutes['PUT /account/franchisee/update']
          }
          // 处理代理商删除API的动态路由
          else if (method === 'DELETE' && url.match(/^\/account\/franchisee\/delete\/\d+/)) {
            handler = mockRoutes['DELETE /account/franchisee/delete']
          }
          // 处理代理商审核API的动态路由
          else if (method === 'PUT' && url.match(/^\/account\/franchisee\/audit\/\d+/)) {
            handler = mockRoutes['PUT /account/franchisee/audit']
          }
          // 处理第三方更新API的动态路由
          else if (method === 'PUT' && url.match(/^\/account\/third\/update\/\d+/)) {
            handler = mockRoutes['PUT /account/third/update']
          }
          // 处理第三方删除API的动态路由
          else if (method === 'DELETE' && url.match(/^\/account\/third\/delete\/\d+/)) {
            handler = mockRoutes['DELETE /account/third/delete']
          }
          // 处理第三方状态切换API的动态路由
          else if (method === 'PUT' && url.match(/^\/account\/third\/status\/\d+/)) {
            handler = mockRoutes['PUT /account/third/status']
          }
          // 处理第三方配置API的动态路由
          else if (method === 'GET' && url.match(/^\/account\/third\/config\/\d+/)) {
            handler = mockRoutes['GET /account/third/config']
          }
          // 处理第三方配置保存API的动态路由
          else if (method === 'PUT' && url.match(/^\/account\/third\/config\/\d+/)) {
            handler = mockRoutes['PUT /account/third/config']
          }
          // 处理微信审核详情API的动态路由
          else if (method === 'GET' && url.match(/^\/system\/examine\/detail\/\d+/)) {
            handler = mockRoutes['GET /system/examine/detail']
          }
          // 处理微信审核重新提交API的动态路由
          else if (method === 'PUT' && url.match(/^\/system\/examine\/resubmit\/\d+/)) {
            handler = mockRoutes['PUT /system/examine/resubmit']
          }
          // 处理版本详情API的动态路由
          else if (method === 'GET' && url.match(/^\/system\/version\/detail\/\d+/)) {
            handler = mockRoutes['GET /system/version/detail']
          }
          // 处理版本回滚API的动态路由
          else if (method === 'PUT' && url.match(/^\/system\/version\/rollback\/\d+/)) {
            handler = mockRoutes['PUT /system/version/rollback']
          }
        }

        if (handler) {
          console.log(`✅ 找到Mock处理器: ${routeKey}`)
          try {
            handler(req, res)
          } catch (error) {
            console.error('❌ Mock处理器执行错误:', error)
            res.statusCode = 500
            res.setHeader('Content-Type', 'application/json')
            res.end(JSON.stringify({ code: 500, message: '服务器内部错误', data: null }))
          }
        } else {
          console.log(`❌ 未找到Mock处理器: ${routeKey}`)
          next()
        }
      })

      console.log('✅ Vite Mock插件已加载')
      console.log('📊 Mock数据统计:')
      console.log(`   - 服务项目: ${serviceList.length} 条`)
      console.log(`   - 轮播图: ${bannerList.length} 条`)
      console.log(`   - 分类: ${categoryList.length} 条`)
      console.log('🔧 已注册的Mock API路由:')
      Object.keys(mockRoutes).forEach(route => {
        console.log(`   - ${route}`)
      })
    }
  }
}
